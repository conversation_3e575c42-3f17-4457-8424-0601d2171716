# WebRTC 连接问题解决方案

## 🔍 问题分析

根据测试页面的日志显示，JSWebRTC播放器创建成功，但一直停留在"正在初始化播放器"状态，这表明WebRTC连接建立过程中遇到了问题。

### 问题原因分析

1. **服务器连接问题**
   - JSWebRTC需要向 `http://live2.sahy.cloud:1985/rtc/v1/play/` 发送POST请求
   - 服务器可能不可达或端口被阻止

2. **CORS跨域问题**
   - 浏览器阻止了跨域请求
   - 服务器未配置正确的CORS头

3. **协议不匹配**
   - 当前使用HTTP协议，但服务器可能要求HTTPS
   - WebRTC在生产环境通常需要HTTPS

4. **服务器配置问题**
   - WebRTC服务未正确启动
   - SRS服务器配置不正确

## 🛠 解决方案

### 方案1：检查服务器状态

```bash
# 测试服务器连通性
curl -X POST http://live2.sahy.cloud:1985/rtc/v1/play/ \
  -H "Content-Type: application/json" \
  -d '{"api":"http://live2.sahy.cloud:1985/rtc/v1/play/","streamurl":"webrtc://live2.sahy.cloud/live/7CTDM3900BM6WS-165-0-7","clientip":null,"sdp":"test"}'

# 检查端口是否开放
telnet live2.sahy.cloud 1985
```

### 方案2：使用HTTPS协议

修改URL为HTTPS版本：
```javascript
// 原始URL
'webrtc://live2.sahy.cloud/live/7CTDM3900BM6WS-165-0-7'

// 可能的HTTPS版本
'webrtc://live2.sahy.cloud/live/7CTDM3900BM6WS-165-0-7?schema=https'
```

### 方案3：使用官方演示服务器

测试已知可用的WebRTC服务器：

```javascript
// SRS官方演示服务器
'webrtc://r.ossrs.net/live/livestream'
'webrtc://demo.ossrs.net/live/livestream'

// 其他公开测试服务器
'webrtc://webrtc.ossrs.net/live/livestream'
```

### 方案4：本地SRS服务器

如果需要使用自己的服务器，可以搭建本地SRS服务器：

```bash
# 使用Docker运行SRS
docker run --rm -it -p 1935:1935 -p 1985:1985 -p 8080:8080 \
  ossrs/srs:4 ./objs/srs -c conf/rtc.conf

# 测试URL
'webrtc://localhost/live/livestream'
```

### 方案5：修改JSWebRTC配置

创建自定义配置的JSWebRTC播放器：

```javascript
// 在JSWebRTCPlayer组件中添加配置选项
const player = new JSWebrtc.Player(url, {
  video: videoDom,
  autoplay: this.autoplay,
  // 自定义API端点
  api: 'https://live2.sahy.cloud:1985/rtc/v1/play/',
  // 添加超时设置
  timeout: 10000,
  onPlay: (obj) => {
    console.log('播放成功')
  },
  onError: (error) => {
    console.error('播放失败:', error)
  }
})
```

## 🔧 调试步骤

### 1. 使用增强版测试页面

打开 `test-jswebrtc.html` 并：

1. 点击"测试连接"按钮
2. 查看控制台日志
3. 尝试不同的测试URL
4. 检查网络请求状态

### 2. 检查浏览器开发者工具

1. **Network标签**：
   - 查看是否有失败的HTTP请求
   - 检查请求状态码和响应

2. **Console标签**：
   - 查看JSWebRTC的内部日志
   - 检查是否有CORS错误

3. **Security标签**：
   - 确认HTTPS/HTTP协议状态
   - 检查证书问题

### 3. 服务器端检查

如果您有服务器访问权限：

```bash
# 检查SRS服务状态
systemctl status srs

# 查看SRS日志
tail -f /var/log/srs.log

# 检查端口监听
netstat -tlnp | grep 1985

# 测试WebRTC配置
curl http://localhost:1985/api/v1/versions
```

## 🎯 推荐解决方案

### 立即可行的方案

1. **使用SRS官方演示服务器**：
   ```javascript
   // 在测试页面中尝试
   'webrtc://r.ossrs.net/live/livestream'
   ```

2. **检查原始服务器状态**：
   - 联系 `live2.sahy.cloud` 的管理员
   - 确认WebRTC服务是否正常运行
   - 检查防火墙和网络配置

3. **使用HTTPS环境**：
   - 部署到HTTPS服务器
   - 或使用本地HTTPS开发环境

### 长期解决方案

1. **搭建自己的SRS服务器**：
   ```bash
   # 安装SRS
   git clone https://github.com/ossrs/srs.git
   cd srs/trunk
   ./configure
   make
   
   # 配置WebRTC
   ./objs/srs -c conf/rtc.conf
   ```

2. **配置NGINX代理**：
   ```nginx
   server {
       listen 443 ssl;
       server_name your-domain.com;
       
       location /rtc/ {
           proxy_pass http://localhost:1985/rtc/;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
       }
   }
   ```

## 📋 故障排除清单

- [ ] 服务器 `live2.sahy.cloud` 是否在线？
- [ ] 端口 1985 是否开放？
- [ ] 是否存在CORS问题？
- [ ] 是否需要HTTPS协议？
- [ ] SRS服务器是否正确配置WebRTC？
- [ ] 防火墙是否阻止了连接？
- [ ] 网络环境是否支持WebRTC？

## 🔄 替代方案

如果WebRTC仍然无法工作，可以考虑：

1. **使用HLS协议**：
   ```javascript
   'https://live2.sahy.cloud/live/7CTDM3900BM6WS-165-0-7/index.m3u8'
   ```

2. **使用FLV协议**：
   ```javascript
   'https://live2.sahy.cloud/live/7CTDM3900BM6WS-165-0-7.flv'
   ```

3. **使用RTMP协议**（需要Flash）：
   ```javascript
   'rtmp://live2.sahy.cloud/live/7CTDM3900BM6WS-165-0-7'
   ```

## 📞 获取帮助

如果问题仍然存在：

1. **检查SRS官方文档**：https://ossrs.net/
2. **查看JSWebRTC GitHub**：https://github.com/kernelj/jswebrtc
3. **联系服务器管理员**确认WebRTC服务状态
4. **在浏览器中测试官方演示**：https://ossrs.net/players/

记住：WebRTC是一个复杂的协议，需要服务器端正确配置才能正常工作。
