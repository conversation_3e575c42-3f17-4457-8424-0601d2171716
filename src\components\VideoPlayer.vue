<template>
  <div class="video-player-container">
    <div class="video-header">
      <span class="video-title">{{ deviceName }}</span>
      <div class="video-controls">
        <el-button 
          type="text" 
          icon="el-icon-refresh" 
          @click="refreshVideo" 
          class="control-btn"
          title="刷新视频"
        />
        <el-button 
          type="text" 
          icon="el-icon-full-screen" 
          @click="toggleFullscreen" 
          class="control-btn"
          title="全屏"
        />
        <el-button 
          type="text" 
          icon="el-icon-close" 
          @click="closeVideo" 
          class="control-btn close-btn"
          title="关闭"
        />
      </div>
    </div>
    <div class="video-wrapper">
      <video
        ref="videoPlayer"
        :id="playerId"
        class="video-js vjs-default-skin"
        controls
        preload="auto"
        :width="width"
        :height="height"
        data-setup="{}"
      >
        <p class="vjs-no-js">
          To view this video please enable JavaScript, and consider upgrading to a web browser that
          <a href="https://videojs.com/html5-video-support/" target="_blank">supports HTML5 video</a>.
        </p>
      </video>
      <div v-if="loading" class="video-loading">
        <i class="el-icon-loading"></i>
        <span>视频加载中...</span>
      </div>
      <div v-if="error" class="video-error">
        <i class="el-icon-warning"></i>
        <span>{{ error }}</span>
        <div class="error-actions">
          <el-button type="primary" size="mini" @click="retryLoad">重试</el-button>
          <el-button v-if="isWebRTCUrl" type="success" size="mini" @click="showAlternativeUrls">查看替代方案</el-button>
        </div>
      </div>

      <!-- 替代URL对话框 -->
      <el-dialog
        title="WebRTC视频播放替代方案"
        :visible.sync="showAlternativeDialog"
        width="600px"
        append-to-body
      >
        <div class="alternative-urls">
          <p>WebRTC协议需要特殊支持，请尝试以下替代URL：</p>
          <div v-for="(urlObj, index) in alternativeUrls" :key="index" class="url-option">
            <div class="url-info">
              <span class="url-type">{{ urlObj.type }}</span>
              <code class="url-text">{{ urlObj.url }}</code>
            </div>
            <el-button size="mini" @click="tryAlternativeUrl(urlObj.url)">尝试播放</el-button>
          </div>
        </div>
        <div slot="footer">
          <el-button @click="showAlternativeDialog = false">关闭</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import videojs from 'video.js'
import 'video.js/dist/video-js.css'
import '@videojs/http-streaming'

export default {
  name: 'VideoPlayer',
  props: {
    src: {
      type: String,
      required: true
    },
    deviceName: {
      type: String,
      default: '视频播放器'
    },
    width: {
      type: [String, Number],
      default: 400
    },
    height: {
      type: [String, Number],
      default: 250
    },
    autoplay: {
      type: Boolean,
      default: false
    },
    muted: {
      type: Boolean,
      default: true
    },
    loop: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      player: null,
      webrtcPlayer: null,
      playerId: `video-player-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      loading: false,
      error: null,
      retryCount: 0,
      maxRetries: 3,
      playerType: 'videojs', // videojs 或 webrtc
      showAlternativeDialog: false,
      alternativeUrls: []
    }
  },
  computed: {
    isWebRTCUrl() {
      return this.src && this.src.toLowerCase().startsWith('webrtc://')
    }
  },
  mounted() {
    this.initPlayer()
  },
  beforeDestroy() {
    this.destroyPlayer()
  },
  watch: {
    src(newSrc) {
      if (this.player) {
        this.loadVideo(newSrc)
      }
    }
  },
  methods: {
    initPlayer() {
      this.loading = true
      this.error = null

      // 检测视频协议类型
      this.detectPlayerType()

      if (this.playerType === 'webrtc') {
        this.initWebRTCPlayer()
      } else {
        this.initVideoJSPlayer()
      }
    },

    detectPlayerType() {
      const url = this.src.toLowerCase()
      if (url.startsWith('webrtc://') || url.includes('webrtc')) {
        this.playerType = 'webrtc'
      } else {
        this.playerType = 'videojs'
      }
    },

    initWebRTCPlayer() {
      try {
        // 直接显示友好的错误信息，提供替代方案
        this.error = 'WebRTC协议需要特殊支持，当前播放器暂不支持此协议'
        this.loading = false

        console.log('检测到WebRTC协议:', this.src)
        console.log('建议使用HLS、RTMP或HTTP协议的视频源')

      } catch (error) {
        console.error('初始化WebRTC播放器失败:', error)
        this.error = 'WebRTC播放器初始化失败，请尝试其他协议'
        this.loading = false
      }
    },

    generatePossibleUrls(webrtcUrl) {
      try {
        const url = new URL(webrtcUrl.replace('webrtc://', 'https://'))

        return [
          // HLS格式
          `${url.origin}${url.pathname}/index.m3u8`,
          `${url.origin}${url.pathname}.m3u8`,
          `${url.origin}${url.pathname}/playlist.m3u8`,

          // FLV格式
          `${url.origin}${url.pathname}.flv`,
          `${url.origin}${url.pathname}/index.flv`,

          // MP4格式
          `${url.origin}${url.pathname}.mp4`,

          // RTMP格式
          webrtcUrl.replace('webrtc://', 'rtmp://'),

          // HTTP直播流
          `${url.origin}${url.pathname}`,

          // 尝试HTTP协议
          webrtcUrl.replace('webrtc://', 'http://'),
          webrtcUrl.replace('webrtc://', 'https://')
        ]
      } catch (error) {
        console.error('生成可能的URL失败:', error)
        return []
      }
    },

    tryMultipleUrls(urls, index) {
      if (index >= urls.length) {
        this.error = '所有视频格式都无法播放，请检查视频源'
        this.loading = false
        return
      }

      const currentUrl = urls[index]
      console.log(`尝试播放URL (${index + 1}/${urls.length}):`, currentUrl)

      // 创建临时播放器来测试URL
      this.testUrlAndPlay(currentUrl, () => {
        // 成功回调
        console.log('URL播放成功:', currentUrl)
      }, () => {
        // 失败回调，尝试下一个URL
        console.log('URL播放失败，尝试下一个:', currentUrl)
        setTimeout(() => {
          this.tryMultipleUrls(urls, index + 1)
        }, 1000)
      })
    },

    testUrlAndPlay(url, onSuccess, onError) {
      try {
        // 销毁现有播放器
        if (this.player) {
          this.player.dispose()
          this.player = null
        }

        const options = {
          controls: true,
          responsive: true,
          fluid: false,
          autoplay: this.autoplay,
          muted: this.muted,
          preload: 'auto',
          width: this.width,
          height: this.height,
          html5: {
            vhs: {
              overrideNative: true
            }
          },
          techOrder: ['html5'],
          sources: [{
            src: url,
            type: this.getVideoType(url)
          }]
        }

        this.player = videojs(this.$refs.videoPlayer, options)

        // 设置成功和失败的监听器
        let hasResolved = false

        this.player.ready(() => {
          console.log('播放器准备就绪，测试URL:', url)
        })

        this.player.on('canplay', () => {
          if (!hasResolved) {
            hasResolved = true
            this.loading = false
            this.retryCount = 0
            this.setupEventListeners()
            onSuccess()
          }
        })

        this.player.on('error', () => {
          if (!hasResolved) {
            hasResolved = true
            onError()
          }
        })

        // 设置超时
        setTimeout(() => {
          if (!hasResolved) {
            hasResolved = true
            onError()
          }
        }, 5000)

      } catch (error) {
        console.error('测试URL失败:', error)
        onError()
      }
    },

    initVideoJSPlayer() {
      try {
        // Video.js 配置
        const options = {
          controls: true,
          responsive: true,
          fluid: false,
          autoplay: this.autoplay,
          muted: this.muted,
          loop: this.loop,
          preload: 'auto',
          width: this.width,
          height: this.height,
          html5: {
            vhs: {
              overrideNative: true
            },
            nativeVideoTracks: false,
            nativeAudioTracks: false,
            nativeTextTracks: false
          },
          // 移除flash支持，只使用html5
          techOrder: ['html5'],
          sources: [{
            src: this.src,
            type: this.getVideoType(this.src)
          }]
        }

        // 初始化播放器
        this.player = videojs(this.$refs.videoPlayer, options)

        // 事件监听
        this.setupEventListeners()

        // 加载视频
        this.loadVideo(this.src)

      } catch (error) {
        console.error('初始化Video.js播放器失败:', error)
        this.error = '初始化播放器失败'
        this.loading = false
      }
    },

    convertWebRTCUrl(webrtcUrl) {
      // 尝试多种可能的URL转换格式
      try {
        const url = new URL(webrtcUrl.replace('webrtc://', 'https://'))

        // 可能的转换格式列表
        const possibleUrls = [
          // HLS格式
          `${url.origin}${url.pathname}/index.m3u8`,
          `${url.origin}${url.pathname}.m3u8`,
          `${url.origin}${url.pathname}/playlist.m3u8`,

          // FLV格式
          `${url.origin}${url.pathname}.flv`,
          `${url.origin}${url.pathname}/index.flv`,

          // MP4格式
          `${url.origin}${url.pathname}.mp4`,
          `${url.origin}${url.pathname}/index.mp4`,

          // RTMP格式
          webrtcUrl.replace('webrtc://', 'rtmp://'),

          // HTTP-FLV格式
          `${url.origin}${url.pathname}.flv`,

          // WebSocket-FLV格式
          webrtcUrl.replace('webrtc://', 'ws://') + '.flv'
        ]

        console.log('尝试转换WebRTC URL，可能的格式:', possibleUrls)

        // 返回第一个可能的URL，后续会依次尝试
        return possibleUrls[0]
      } catch (error) {
        console.error('转换WebRTC URL失败:', error)
        return null
      }
    },

    initVideoJSPlayerWithUrl(url) {
      try {
        // Video.js 配置
        const options = {
          controls: true,
          responsive: true,
          fluid: false,
          autoplay: this.autoplay,
          muted: this.muted,
          loop: this.loop,
          preload: 'auto',
          width: this.width,
          height: this.height,
          html5: {
            vhs: {
              overrideNative: true
            },
            nativeVideoTracks: false,
            nativeAudioTracks: false,
            nativeTextTracks: false
          },
          techOrder: ['html5'],
          sources: [{
            src: url,
            type: this.getVideoType(url)
          }]
        }

        // 初始化播放器
        this.player = videojs(this.$refs.videoPlayer, options)

        // 事件监听
        this.setupEventListeners()

        // 加载视频
        this.player.load()

      } catch (error) {
        console.error('初始化Video.js播放器失败:', error)
        this.error = '初始化播放器失败'
        this.loading = false
      }
    },

    setupEventListeners() {
      if (!this.player) return
      
      // 播放器准备就绪
      this.player.ready(() => {
        console.log('视频播放器准备就绪')
      })
      
      // 视频加载开始
      this.player.on('loadstart', () => {
        this.loading = true
        this.error = null
      })
      
      // 视频可以播放
      this.player.on('canplay', () => {
        this.loading = false
        this.retryCount = 0
      })
      
      // 视频开始播放
      this.player.on('play', () => {
        this.loading = false
      })
      
      // 视频暂停
      this.player.on('pause', () => {
        console.log('视频暂停')
      })
      
      // 视频结束
      this.player.on('ended', () => {
        console.log('视频播放结束')
      })
      
      // 错误处理
      this.player.on('error', (error) => {
        console.error('视频播放错误:', error)
        this.loading = false
        this.handleVideoError()
      })
      
      // 网络状态变化
      this.player.on('waiting', () => {
        this.loading = true
      })
      
      this.player.on('playing', () => {
        this.loading = false
      })
    },
    
    loadVideo(src) {
      if (!src) return

      // 重新检测播放器类型
      this.detectPlayerType()

      if (this.playerType === 'webrtc') {
        this.loadWebRTCVideo(src)
      } else {
        this.loadVideoJSVideo(src)
      }
    },

    loadWebRTCVideo(src) {
      if (!this.webrtcPlayer) return

      try {
        this.loading = true
        this.error = null

        const streamId = this.extractStreamId(src)
        this.webrtcPlayer.init(this.$refs.videoPlayer, streamId)

      } catch (error) {
        console.error('加载WebRTC视频失败:', error)
        this.handleVideoError()
      }
    },

    loadVideoJSVideo(src) {
      if (!this.player) return

      try {
        this.loading = true
        this.error = null

        // 设置视频源
        this.player.src({
          src: src,
          type: this.getVideoType(src)
        })

        // 加载视频
        this.player.load()

      } catch (error) {
        console.error('加载Video.js视频失败:', error)
        this.handleVideoError()
      }
    },
    
    getVideoType(src) {
      if (!src) return 'video/mp4'
      
      const url = src.toLowerCase()
      
      // RTMP 流
      if (url.startsWith('rtmp://')) {
        return 'rtmp/mp4'
      }
      // RTSP 流
      else if (url.startsWith('rtsp://')) {
        return 'application/x-rtsp'
      }
      // HLS 流
      else if (url.includes('.m3u8')) {
        return 'application/x-mpegURL'
      }
      // DASH 流
      else if (url.includes('.mpd')) {
        return 'application/dash+xml'
      }
      // WebRTC
      else if (url.startsWith('webrtc://')) {
        return 'application/webrtc'
      }
      // HTTP 流
      else if (url.startsWith('http')) {
        if (url.includes('.mp4')) return 'video/mp4'
        if (url.includes('.webm')) return 'video/webm'
        if (url.includes('.ogg')) return 'video/ogg'
        if (url.includes('.flv')) return 'video/x-flv'
        return 'video/mp4' // 默认
      }
      
      return 'video/mp4'
    },
    
    handleVideoError() {
      if (this.retryCount < this.maxRetries) {
        this.retryCount++
        console.log(`视频加载失败，正在重试 (${this.retryCount}/${this.maxRetries})`)
        setTimeout(() => {
          this.retryLoad()
        }, 2000)
      } else {
        this.error = '视频加载失败，请检查网络连接或视频源'
        this.loading = false
      }
    },
    
    retryLoad() {
      this.loadVideo(this.src)
    },
    
    refreshVideo() {
      this.retryCount = 0
      if (this.playerType === 'webrtc' && this.webrtcPlayer) {
        this.webrtcPlayer.destroy()
        this.initWebRTCPlayer()
      } else {
        this.loadVideo(this.src)
      }
    },
    
    toggleFullscreen() {
      if (this.player) {
        if (this.player.isFullscreen()) {
          this.player.exitFullscreen()
        } else {
          this.player.requestFullscreen()
        }
      }
    },
    
    closeVideo() {
      this.$emit('close')
    },
    
    destroyPlayer() {
      // 销毁Video.js播放器
      if (this.player) {
        try {
          this.player.dispose()
          this.player = null
        } catch (error) {
          console.error('销毁Video.js播放器失败:', error)
        }
      }

      // 销毁WebRTC播放器
      if (this.webrtcPlayer) {
        try {
          this.webrtcPlayer.destroy()
          this.webrtcPlayer = null
        } catch (error) {
          console.error('销毁WebRTC播放器失败:', error)
        }
      }
    },
    
    // 公共方法
    play() {
      if (this.playerType === 'webrtc') {
        // WebRTC播放器通常自动播放
        if (this.$refs.videoPlayer) {
          this.$refs.videoPlayer.play()
        }
      } else if (this.player) {
        this.player.play()
      }
    },

    pause() {
      if (this.playerType === 'webrtc') {
        if (this.$refs.videoPlayer) {
          this.$refs.videoPlayer.pause()
        }
      } else if (this.player) {
        this.player.pause()
      }
    },

    stop() {
      if (this.playerType === 'webrtc') {
        if (this.$refs.videoPlayer) {
          this.$refs.videoPlayer.pause()
          this.$refs.videoPlayer.currentTime = 0
        }
      } else if (this.player) {
        this.player.pause()
        this.player.currentTime(0)
      }
    },

    setVolume(volume) {
      if (this.playerType === 'webrtc') {
        if (this.$refs.videoPlayer) {
          this.$refs.videoPlayer.volume = volume
        }
      } else if (this.player) {
        this.player.volume(volume)
      }
    },

    mute() {
      if (this.playerType === 'webrtc') {
        if (this.$refs.videoPlayer) {
          this.$refs.videoPlayer.muted = true
        }
      } else if (this.player) {
        this.player.muted(true)
      }
    },

    unmute() {
      if (this.playerType === 'webrtc') {
        if (this.$refs.videoPlayer) {
          this.$refs.videoPlayer.muted = false
        }
      } else if (this.player) {
        this.player.muted(false)
      }
    },

    showAlternativeUrls() {
      this.alternativeUrls = this.generatePossibleUrls(this.src).map(url => ({
        url: url,
        type: this.getUrlType(url)
      }))
      this.showAlternativeDialog = true
    },

    getUrlType(url) {
      if (url.includes('.m3u8')) return 'HLS'
      if (url.includes('.flv')) return 'FLV'
      if (url.includes('.mp4')) return 'MP4'
      if (url.startsWith('rtmp://')) return 'RTMP'
      if (url.startsWith('http://')) return 'HTTP'
      if (url.startsWith('https://')) return 'HTTPS'
      return '未知'
    },

    tryAlternativeUrl(url) {
      this.showAlternativeDialog = false
      this.error = null
      this.retryCount = 0

      // 临时更改src来测试新URL
      const originalSrc = this.src
      this.$emit('update:src', url)

      // 如果播放失败，恢复原始URL
      setTimeout(() => {
        if (this.error) {
          this.$emit('update:src', originalSrc)
        }
      }, 10000)

      this.loadVideo(url)
    }
  }
}
</script>

<style scoped>
.video-player-container {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
}

.video-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.video-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  flex: 1;
}

.video-controls {
  display: flex;
  align-items: center;
  gap: 5px;
}

.control-btn {
  padding: 5px;
  font-size: 16px;
  color: #606266;
  transition: color 0.3s;
}

.control-btn:hover {
  color: #409EFF;
}

.close-btn:hover {
  color: #f56c6c;
}

.video-wrapper {
  position: relative;
  background: #000;
  width: 100%;
  height: 250px; /* 固定高度 */
}

.video-js {
  width: 100% !important;
  height: 250px !important; /* 固定高度 */
  display: block;
}

.video-loading,
.video-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #fff;
  z-index: 10;
  background: rgba(0, 0, 0, 0.7);
  padding: 20px;
  border-radius: 4px;
  min-width: 150px;
}

.video-loading i,
.video-error i {
  font-size: 24px;
  margin-bottom: 10px;
}

.video-loading span,
.video-error span {
  font-size: 14px;
  margin-bottom: 10px;
  text-align: center;
}

.error-actions {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.video-error .el-button {
  margin: 0;
}

/* Video.js 自定义样式 */
.video-js .vjs-big-play-button {
  background-color: rgba(64, 158, 255, 0.8);
  border: none;
  border-radius: 50%;
  width: 80px;
  height: 80px;
  line-height: 80px;
  font-size: 24px;
  top: 50%;
  left: 50%;
  margin-top: -40px;
  margin-left: -40px;
}

.video-js .vjs-big-play-button:hover {
  background-color: rgba(64, 158, 255, 1);
}

.video-js .vjs-control-bar {
  background: rgba(0, 0, 0, 0.7);
  height: 40px;
}

.video-js .vjs-progress-control {
  height: 40px;
}

.video-js .vjs-progress-holder {
  height: 6px;
  margin: 17px 0;
}

.video-js .vjs-play-progress {
  background-color: #409EFF;
}

.video-js .vjs-volume-level {
  background-color: #409EFF;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .video-header {
    padding: 8px 12px;
  }

  .video-title {
    font-size: 12px;
  }

  .control-btn {
    font-size: 14px;
    padding: 3px;
  }

  .video-loading,
  .video-error {
    padding: 15px;
    min-width: 120px;
  }

  .video-loading i,
  .video-error i {
    font-size: 20px;
  }

  .video-loading span,
  .video-error span {
    font-size: 12px;
  }
}

/* 全屏模式样式 */
.video-js.vjs-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 9999;
}

.video-js.vjs-fullscreen .vjs-tech {
  width: 100% !important;
  height: 100% !important;
}

/* 替代URL对话框样式 */
.alternative-urls {
  max-height: 400px;
  overflow-y: auto;
}

.url-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px;
  margin: 8px 0;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: #f9f9f9;
}

.url-info {
  flex: 1;
  margin-right: 10px;
}

.url-type {
  display: inline-block;
  background: #409EFF;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  margin-right: 10px;
  min-width: 50px;
  text-align: center;
}

.url-text {
  font-family: monospace;
  font-size: 12px;
  color: #606266;
  word-break: break-all;
  background: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
  display: block;
  margin-top: 5px;
}
</style>
