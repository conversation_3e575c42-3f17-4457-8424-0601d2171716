<template>
  <div class="video-player-container">
    <div class="video-header">
      <span class="video-title">{{ deviceName }}</span>
      <div class="video-controls">
        <el-button 
          type="text" 
          icon="el-icon-refresh" 
          @click="refreshVideo" 
          class="control-btn"
          title="刷新视频"
        />
        <el-button 
          type="text" 
          icon="el-icon-full-screen" 
          @click="toggleFullscreen" 
          class="control-btn"
          title="全屏"
        />
        <el-button 
          type="text" 
          icon="el-icon-close" 
          @click="closeVideo" 
          class="control-btn close-btn"
          title="关闭"
        />
      </div>
    </div>
    <div class="video-wrapper">
      <video
        ref="videoPlayer"
        :id="playerId"
        class="video-js vjs-default-skin"
        controls
        preload="auto"
        :width="width"
        :height="height"
        data-setup="{}"
      >
        <p class="vjs-no-js">
          To view this video please enable JavaScript, and consider upgrading to a web browser that
          <a href="https://videojs.com/html5-video-support/" target="_blank">supports HTML5 video</a>.
        </p>
      </video>
      <div v-if="loading" class="video-loading">
        <i class="el-icon-loading"></i>
        <span>视频加载中...</span>
      </div>
      <div v-if="error" class="video-error">
        <i class="el-icon-warning"></i>
        <span>{{ error }}</span>
        <el-button type="primary" size="mini" @click="retryLoad">重试</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import videojs from 'video.js'
import 'video.js/dist/video-js.css'
import '@videojs/http-streaming'
import WebRTCPlayer from '@/utils/webrtc-player.js'

export default {
  name: 'VideoPlayer',
  props: {
    src: {
      type: String,
      required: true
    },
    deviceName: {
      type: String,
      default: '视频播放器'
    },
    width: {
      type: [String, Number],
      default: 400
    },
    height: {
      type: [String, Number],
      default: 250
    },
    autoplay: {
      type: Boolean,
      default: false
    },
    muted: {
      type: Boolean,
      default: true
    },
    loop: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      player: null,
      webrtcPlayer: null,
      playerId: `video-player-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      loading: false,
      error: null,
      retryCount: 0,
      maxRetries: 3,
      playerType: 'videojs' // videojs 或 webrtc
    }
  },
  mounted() {
    this.initPlayer()
  },
  beforeDestroy() {
    this.destroyPlayer()
  },
  watch: {
    src(newSrc) {
      if (this.player) {
        this.loadVideo(newSrc)
      }
    }
  },
  methods: {
    initPlayer() {
      this.loading = true
      this.error = null

      // 检测视频协议类型
      this.detectPlayerType()

      if (this.playerType === 'webrtc') {
        this.initWebRTCPlayer()
      } else {
        this.initVideoJSPlayer()
      }
    },

    detectPlayerType() {
      const url = this.src.toLowerCase()
      if (url.startsWith('webrtc://') || url.includes('webrtc')) {
        this.playerType = 'webrtc'
      } else {
        this.playerType = 'videojs'
      }
    },

    initWebRTCPlayer() {
      try {
        // 暂时使用简化的WebRTC处理
        // 将webrtc://转换为http://以便使用HLS或其他兼容协议
        const convertedUrl = this.convertWebRTCUrl(this.src)

        if (convertedUrl) {
          // 使用转换后的URL初始化Video.js播放器
          this.playerType = 'videojs'
          this.initVideoJSPlayerWithUrl(convertedUrl)
        } else {
          // 如果无法转换，显示错误信息
          this.error = 'WebRTC协议暂不支持，请使用HLS或RTMP协议'
          this.loading = false
        }

      } catch (error) {
        console.error('初始化WebRTC播放器失败:', error)
        this.error = 'WebRTC播放器初始化失败，请尝试其他协议'
        this.loading = false
      }
    },

    initVideoJSPlayer() {
      try {
        // Video.js 配置
        const options = {
          controls: true,
          responsive: true,
          fluid: false,
          autoplay: this.autoplay,
          muted: this.muted,
          loop: this.loop,
          preload: 'auto',
          width: this.width,
          height: this.height,
          html5: {
            vhs: {
              overrideNative: true
            },
            nativeVideoTracks: false,
            nativeAudioTracks: false,
            nativeTextTracks: false
          },
          // 移除flash支持，只使用html5
          techOrder: ['html5'],
          sources: [{
            src: this.src,
            type: this.getVideoType(this.src)
          }]
        }

        // 初始化播放器
        this.player = videojs(this.$refs.videoPlayer, options)

        // 事件监听
        this.setupEventListeners()

        // 加载视频
        this.loadVideo(this.src)

      } catch (error) {
        console.error('初始化Video.js播放器失败:', error)
        this.error = '初始化播放器失败'
        this.loading = false
      }
    },

    convertWebRTCUrl(webrtcUrl) {
      // 尝试将WebRTC URL转换为HLS URL
      // webrtc://live2.sahy.cloud/live/7CTDM3900BM6WS-165-0-7
      // 转换为 https://live2.sahy.cloud/live/7CTDM3900BM6WS-165-0-7/index.m3u8
      try {
        const url = new URL(webrtcUrl.replace('webrtc://', 'https://'))
        const hlsUrl = `${url.origin}${url.pathname}/index.m3u8`
        console.log('转换WebRTC URL为HLS:', hlsUrl)
        return hlsUrl
      } catch (error) {
        console.error('转换WebRTC URL失败:', error)
        return null
      }
    },

    initVideoJSPlayerWithUrl(url) {
      try {
        // Video.js 配置
        const options = {
          controls: true,
          responsive: true,
          fluid: false,
          autoplay: this.autoplay,
          muted: this.muted,
          loop: this.loop,
          preload: 'auto',
          width: this.width,
          height: this.height,
          html5: {
            vhs: {
              overrideNative: true
            },
            nativeVideoTracks: false,
            nativeAudioTracks: false,
            nativeTextTracks: false
          },
          techOrder: ['html5'],
          sources: [{
            src: url,
            type: this.getVideoType(url)
          }]
        }

        // 初始化播放器
        this.player = videojs(this.$refs.videoPlayer, options)

        // 事件监听
        this.setupEventListeners()

        // 加载视频
        this.player.load()

      } catch (error) {
        console.error('初始化Video.js播放器失败:', error)
        this.error = '初始化播放器失败'
        this.loading = false
      }
    },

    setupEventListeners() {
      if (!this.player) return
      
      // 播放器准备就绪
      this.player.ready(() => {
        console.log('视频播放器准备就绪')
      })
      
      // 视频加载开始
      this.player.on('loadstart', () => {
        this.loading = true
        this.error = null
      })
      
      // 视频可以播放
      this.player.on('canplay', () => {
        this.loading = false
        this.retryCount = 0
      })
      
      // 视频开始播放
      this.player.on('play', () => {
        this.loading = false
      })
      
      // 视频暂停
      this.player.on('pause', () => {
        console.log('视频暂停')
      })
      
      // 视频结束
      this.player.on('ended', () => {
        console.log('视频播放结束')
      })
      
      // 错误处理
      this.player.on('error', (error) => {
        console.error('视频播放错误:', error)
        this.loading = false
        this.handleVideoError()
      })
      
      // 网络状态变化
      this.player.on('waiting', () => {
        this.loading = true
      })
      
      this.player.on('playing', () => {
        this.loading = false
      })
    },
    
    loadVideo(src) {
      if (!src) return

      // 重新检测播放器类型
      this.detectPlayerType()

      if (this.playerType === 'webrtc') {
        this.loadWebRTCVideo(src)
      } else {
        this.loadVideoJSVideo(src)
      }
    },

    loadWebRTCVideo(src) {
      if (!this.webrtcPlayer) return

      try {
        this.loading = true
        this.error = null

        const streamId = this.extractStreamId(src)
        this.webrtcPlayer.init(this.$refs.videoPlayer, streamId)

      } catch (error) {
        console.error('加载WebRTC视频失败:', error)
        this.handleVideoError()
      }
    },

    loadVideoJSVideo(src) {
      if (!this.player) return

      try {
        this.loading = true
        this.error = null

        // 设置视频源
        this.player.src({
          src: src,
          type: this.getVideoType(src)
        })

        // 加载视频
        this.player.load()

      } catch (error) {
        console.error('加载Video.js视频失败:', error)
        this.handleVideoError()
      }
    },
    
    getVideoType(src) {
      if (!src) return 'video/mp4'
      
      const url = src.toLowerCase()
      
      // RTMP 流
      if (url.startsWith('rtmp://')) {
        return 'rtmp/mp4'
      }
      // RTSP 流
      else if (url.startsWith('rtsp://')) {
        return 'application/x-rtsp'
      }
      // HLS 流
      else if (url.includes('.m3u8')) {
        return 'application/x-mpegURL'
      }
      // DASH 流
      else if (url.includes('.mpd')) {
        return 'application/dash+xml'
      }
      // WebRTC
      else if (url.startsWith('webrtc://')) {
        return 'application/webrtc'
      }
      // HTTP 流
      else if (url.startsWith('http')) {
        if (url.includes('.mp4')) return 'video/mp4'
        if (url.includes('.webm')) return 'video/webm'
        if (url.includes('.ogg')) return 'video/ogg'
        if (url.includes('.flv')) return 'video/x-flv'
        return 'video/mp4' // 默认
      }
      
      return 'video/mp4'
    },
    
    handleVideoError() {
      if (this.retryCount < this.maxRetries) {
        this.retryCount++
        console.log(`视频加载失败，正在重试 (${this.retryCount}/${this.maxRetries})`)
        setTimeout(() => {
          this.retryLoad()
        }, 2000)
      } else {
        this.error = '视频加载失败，请检查网络连接或视频源'
        this.loading = false
      }
    },
    
    retryLoad() {
      this.loadVideo(this.src)
    },
    
    refreshVideo() {
      this.retryCount = 0
      if (this.playerType === 'webrtc' && this.webrtcPlayer) {
        this.webrtcPlayer.destroy()
        this.initWebRTCPlayer()
      } else {
        this.loadVideo(this.src)
      }
    },
    
    toggleFullscreen() {
      if (this.player) {
        if (this.player.isFullscreen()) {
          this.player.exitFullscreen()
        } else {
          this.player.requestFullscreen()
        }
      }
    },
    
    closeVideo() {
      this.$emit('close')
    },
    
    destroyPlayer() {
      // 销毁Video.js播放器
      if (this.player) {
        try {
          this.player.dispose()
          this.player = null
        } catch (error) {
          console.error('销毁Video.js播放器失败:', error)
        }
      }

      // 销毁WebRTC播放器
      if (this.webrtcPlayer) {
        try {
          this.webrtcPlayer.destroy()
          this.webrtcPlayer = null
        } catch (error) {
          console.error('销毁WebRTC播放器失败:', error)
        }
      }
    },
    
    // 公共方法
    play() {
      if (this.playerType === 'webrtc') {
        // WebRTC播放器通常自动播放
        if (this.$refs.videoPlayer) {
          this.$refs.videoPlayer.play()
        }
      } else if (this.player) {
        this.player.play()
      }
    },

    pause() {
      if (this.playerType === 'webrtc') {
        if (this.$refs.videoPlayer) {
          this.$refs.videoPlayer.pause()
        }
      } else if (this.player) {
        this.player.pause()
      }
    },

    stop() {
      if (this.playerType === 'webrtc') {
        if (this.$refs.videoPlayer) {
          this.$refs.videoPlayer.pause()
          this.$refs.videoPlayer.currentTime = 0
        }
      } else if (this.player) {
        this.player.pause()
        this.player.currentTime(0)
      }
    },

    setVolume(volume) {
      if (this.playerType === 'webrtc') {
        if (this.$refs.videoPlayer) {
          this.$refs.videoPlayer.volume = volume
        }
      } else if (this.player) {
        this.player.volume(volume)
      }
    },

    mute() {
      if (this.playerType === 'webrtc') {
        if (this.$refs.videoPlayer) {
          this.$refs.videoPlayer.muted = true
        }
      } else if (this.player) {
        this.player.muted(true)
      }
    },

    unmute() {
      if (this.playerType === 'webrtc') {
        if (this.$refs.videoPlayer) {
          this.$refs.videoPlayer.muted = false
        }
      } else if (this.player) {
        this.player.muted(false)
      }
    }
  }
}
</script>

<style scoped>
.video-player-container {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
}

.video-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.video-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  flex: 1;
}

.video-controls {
  display: flex;
  align-items: center;
  gap: 5px;
}

.control-btn {
  padding: 5px;
  font-size: 16px;
  color: #606266;
  transition: color 0.3s;
}

.control-btn:hover {
  color: #409EFF;
}

.close-btn:hover {
  color: #f56c6c;
}

.video-wrapper {
  position: relative;
  background: #000;
  width: 100%;
  height: 250px; /* 固定高度 */
}

.video-js {
  width: 100% !important;
  height: 250px !important; /* 固定高度 */
  display: block;
}

.video-loading,
.video-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #fff;
  z-index: 10;
  background: rgba(0, 0, 0, 0.7);
  padding: 20px;
  border-radius: 4px;
  min-width: 150px;
}

.video-loading i,
.video-error i {
  font-size: 24px;
  margin-bottom: 10px;
}

.video-loading span,
.video-error span {
  font-size: 14px;
  margin-bottom: 10px;
  text-align: center;
}

.video-error .el-button {
  margin-top: 10px;
}

/* Video.js 自定义样式 */
.video-js .vjs-big-play-button {
  background-color: rgba(64, 158, 255, 0.8);
  border: none;
  border-radius: 50%;
  width: 80px;
  height: 80px;
  line-height: 80px;
  font-size: 24px;
  top: 50%;
  left: 50%;
  margin-top: -40px;
  margin-left: -40px;
}

.video-js .vjs-big-play-button:hover {
  background-color: rgba(64, 158, 255, 1);
}

.video-js .vjs-control-bar {
  background: rgba(0, 0, 0, 0.7);
  height: 40px;
}

.video-js .vjs-progress-control {
  height: 40px;
}

.video-js .vjs-progress-holder {
  height: 6px;
  margin: 17px 0;
}

.video-js .vjs-play-progress {
  background-color: #409EFF;
}

.video-js .vjs-volume-level {
  background-color: #409EFF;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .video-header {
    padding: 8px 12px;
  }

  .video-title {
    font-size: 12px;
  }

  .control-btn {
    font-size: 14px;
    padding: 3px;
  }

  .video-loading,
  .video-error {
    padding: 15px;
    min-width: 120px;
  }

  .video-loading i,
  .video-error i {
    font-size: 20px;
  }

  .video-loading span,
  .video-error span {
    font-size: 12px;
  }
}

/* 全屏模式样式 */
.video-js.vjs-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw !important;
  height: 100vh !important;
  z-index: 9999;
}

.video-js.vjs-fullscreen .vjs-tech {
  width: 100% !important;
  height: 100% !important;
}
</style>
