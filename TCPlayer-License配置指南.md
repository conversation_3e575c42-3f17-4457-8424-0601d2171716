# TCPlayer License 配置指南

## 🚨 问题解决

您遇到的错误码55表示"缺少License URL"，这是TCPlayer的License验证机制。

### 错误信息
```
TCPlayer: ERROR: (CODE:55 undefined) MediaError {code: '55', message: ''}
```

## 🔧 解决方案

### 方案1：使用现有License（推荐）

我发现您的飞控界面已经配置了一个License URL：
```javascript
tcPlayerLicenseUrl: 'https://license.vod2.myqcloud.com/license/v2/1303164718_1/v_cube.license'
```

这个License可能已经过期或域名不匹配。

### 方案2：申请新的免费License

1. **访问腾讯云控制台**：
   ```
   https://console.cloud.tencent.com/vcube/web?tab=player
   ```

2. **申请步骤**：
   - 登录腾讯云账号
   - 选择"播放器 Web 端基础版 License"
   - 点击"免费申请"
   - 填写应用信息和域名
   - 获取License URL

3. **配置License**：
   ```javascript
   // 在 src/views/flightmaster/index.vue 中更新
   tcPlayerLicenseUrl: 'your-new-license-url-here'
   ```

### 方案3：本地开发跳过License

如果只是本地开发测试，可以临时跳过License验证：

```javascript
// 在 src/views/flightmaster/index.vue 中
tcPlayerLicenseUrl: '' // 本地开发可以为空
```

## 📋 License类型说明

### 免费基础版License
- **费用**：免费
- **有效期**：1年
- **域名限制**：最多10个
- **功能**：基础播放功能
- **申请条件**：无特殊要求

### 企业版License
- **费用**：付费
- **有效期**：根据购买时长
- **域名限制**：无限制
- **功能**：完整功能
- **申请条件**：需要购买相应服务

## 🔍 License验证机制

### 验证流程
1. TCPlayer初始化时检查License URL
2. 向腾讯云服务器验证License有效性
3. 检查域名是否在授权列表中
4. 验证License是否过期

### 常见错误码
- **53**：License时间验证失败（过期）
- **54**：License类型错误
- **55**：缺少License URL
- **56**：License域名验证失败

## 🛠 配置示例

### 完整配置示例

```vue
<template>
  <TCPlayerVideo
    :src="videoUrl"
    :device-name="deviceName"
    :license-url="tcPlayerLicenseUrl"
    @close="handleClose"
  />
</template>

<script>
import TCPlayerVideo from '@/components/TCPlayerVideo.vue'

export default {
  components: { TCPlayerVideo },
  data() {
    return {
      videoUrl: 'webrtc://live2.sahy.cloud/live/7CTDM3900BM6WS-165-0-7',
      deviceName: '我的机场',
      // 配置您的License URL
      tcPlayerLicenseUrl: 'https://license.vod2.myqcloud.com/license/v2/xxx/v_cube.license'
    }
  }
}
</script>
```

### 环境变量配置

```javascript
// .env.development (开发环境)
VUE_APP_TCPLAYER_LICENSE_URL=

// .env.production (生产环境)
VUE_APP_TCPLAYER_LICENSE_URL=https://license.vod2.myqcloud.com/license/v2/xxx/v_cube.license
```

```javascript
// 在组件中使用
data() {
  return {
    tcPlayerLicenseUrl: process.env.VUE_APP_TCPLAYER_LICENSE_URL || ''
  }
}
```

## 🔄 立即解决步骤

### 步骤1：检查现有License
```bash
# 测试现有License是否有效
curl -I "https://license.vod2.myqcloud.com/license/v2/1303164718_1/v_cube.license"
```

### 步骤2：申请新License
1. 访问：https://console.cloud.tencent.com/vcube/web?tab=player
2. 点击"免费申请"
3. 填写域名：`localhost` 和您的实际域名
4. 获取新的License URL

### 步骤3：更新配置
```javascript
// 在 src/views/flightmaster/index.vue 中更新第231行
tcPlayerLicenseUrl: 'your-new-license-url-here'
```

### 步骤4：重启项目
```bash
npm run dev
```

## 🎯 快速测试

### 临时解决方案（仅用于测试）

如果您只是想快速测试功能，可以临时修改TCPlayerVideo.vue：

```javascript
// 在 initPlayer 方法中，临时注释License检查
// if (!this.licenseUrl && !this.isLocalhost()) {
//   this.error = '缺少TCPlayer License URL，请配置licenseUrl属性'
//   this.loading = false
//   return
// }
```

**注意**：这只是临时解决方案，生产环境必须配置正确的License。

## 📞 获取帮助

如果问题仍然存在：

1. **检查License状态**：
   - 确认License URL是否可访问
   - 检查域名是否已授权
   - 验证License是否过期

2. **联系腾讯云支持**：
   - 工单系统：https://console.cloud.tencent.com/workorder
   - 技术文档：https://cloud.tencent.com/document/product/881

3. **查看详细日志**：
   - 打开浏览器开发者工具
   - 查看Network标签的License请求
   - 检查Console标签的错误信息

## 🎉 成功标志

配置成功后，您应该看到：
- 控制台输出：`使用License URL: your-license-url`
- 没有错误码55的报错
- 视频播放器正常初始化
- WebRTC视频开始播放或智能降级

记住：License是TCPlayer正常工作的必要条件，请确保在生产环境中正确配置。
