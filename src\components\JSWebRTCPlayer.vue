<template>
  <div class="jswebrtc-container">
    <div class="video-header">
      <span class="video-title">{{ deviceName }}</span>
      <div class="video-controls">
        <el-button 
          type="text" 
          icon="el-icon-refresh" 
          @click="refreshVideo" 
          class="control-btn"
          title="刷新视频"
        />
        <el-button 
          type="text" 
          icon="el-icon-full-screen" 
          @click="toggleFullscreen" 
          class="control-btn"
          title="全屏"
        />
        <el-button 
          type="text" 
          icon="el-icon-close" 
          @click="closeVideo" 
          class="control-btn close-btn"
          title="关闭"
        />
      </div>
    </div>
    
    <div class="video-wrapper">
      <!-- JSWebRTC 视频容器 -->
      <video
        :id="playerId"
        ref="jswebrtcVideo"
        controls
        :width="width"
        :height="height"
        :style="{ width: width + 'px', height: height + 'px', objectFit: 'fill' }"
        preload="auto"
        playsinline
        webkit-playsinline
        x5-playsinline
      >
        您的浏览器不支持视频播放。
      </video>
      
      <!-- 加载状态 -->
      <div v-if="loading" class="video-loading">
        <i class="el-icon-loading"></i>
        <span>{{ loadingText }}</span>
      </div>
      
      <!-- 错误状态 -->
      <div v-if="error" class="video-error">
        <i class="el-icon-warning"></i>
        <span>{{ error }}</span>
        <div class="error-actions">
          <el-button type="primary" size="mini" @click="retryLoad">重试</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'JSWebRTCPlayer',
  props: {
    src: {
      type: String,
      required: true
    },
    deviceName: {
      type: String,
      default: '视频播放器'
    },
    width: {
      type: [String, Number],
      default: 400
    },
    height: {
      type: [String, Number],
      default: 250
    },
    autoplay: {
      type: Boolean,
      default: true
    },
    muted: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      player: null,
      playerId: `jswebrtc-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      loading: false,
      loadingText: '视频加载中...',
      error: null,
      retryCount: 0,
      maxRetries: 3,
      jsWebRTCLoaded: false
    }
  },
  mounted() {
    this.loadJSWebRTC()
  },
  beforeDestroy() {
    this.destroyPlayer()
  },
  watch: {
    src(newSrc) {
      if (newSrc && this.jsWebRTCLoaded) {
        this.initPlayer()
      }
    }
  },
  methods: {
    async loadJSWebRTC() {
      // 检查是否已经加载了JSWebrtc
      if (window.JSWebrtc) {
        this.jsWebRTCLoaded = true
        this.initPlayer()
        return
      }

      // 等待一段时间让全局脚本加载完成
      let retryCount = 0
      const maxRetries = 10

      const checkJSWebRTC = () => {
        if (window.JSWebrtc) {
          this.jsWebRTCLoaded = true
          console.log('JSWebRTC加载成功')
          this.initPlayer()
          return
        }

        retryCount++
        if (retryCount < maxRetries) {
          setTimeout(checkJSWebRTC, 100)
        } else {
          console.error('JSWebRTC加载超时')
          this.error = 'JSWebRTC播放器加载失败，请刷新页面重试'
          this.loading = false
        }
      }

      this.loading = true
      this.loadingText = '加载播放器...'
      checkJSWebRTC()
    },
    
    initPlayer() {
      if (!window.JSWebrtc) {
        this.error = 'JSWebRTC未加载'
        this.loading = false
        return
      }
      
      if (!this.src) {
        this.error = '视频源URL为空'
        this.loading = false
        return
      }
      
      this.loading = true
      this.error = null
      this.loadingText = '初始化播放器...'
      
      try {
        // 销毁现有播放器
        this.destroyPlayer()
        
        // 获取视频DOM元素
        const videoDom = document.getElementById(this.playerId)
        if (!videoDom) {
          throw new Error('视频DOM元素未找到')
        }
        
        console.log('初始化JSWebRTC播放器，URL:', this.src)
        
        // 创建JSWebRTC播放器实例
        this.player = new window.JSWebrtc.Player(this.src, {
          video: videoDom,
          autoplay: this.autoplay,
          onPlay: (obj) => {
            console.log('JSWebRTC播放器开始播放:', obj)
            this.loading = false
            this.retryCount = 0
            
            // 监听video元素状态，确保能够播放
            videoDom.addEventListener('canplay', () => {
              console.log('视频可以播放')
              if (this.autoplay) {
                videoDom.play().catch(error => {
                  console.warn('自动播放失败:', error)
                })
              }
            })
            
            // 监听播放事件
            videoDom.addEventListener('play', () => {
              console.log('视频开始播放')
              this.loading = false
            })
            
            // 监听暂停事件
            videoDom.addEventListener('pause', () => {
              console.log('视频暂停')
            })
            
            // 监听错误事件
            videoDom.addEventListener('error', (e) => {
              console.error('视频播放错误:', e)
              this.handleVideoError()
            })
          },
          onError: (error) => {
            console.error('JSWebRTC播放器错误:', error)
            this.handleVideoError()
          }
        })
        
        this.loadingText = '连接视频流...'
        
      } catch (error) {
        console.error('初始化JSWebRTC播放器失败:', error)
        this.error = '播放器初始化失败: ' + error.message
        this.loading = false
      }
    },
    
    handleVideoError() {
      if (this.retryCount < this.maxRetries) {
        this.retryCount++
        console.log(`视频播放失败，正在重试 (${this.retryCount}/${this.maxRetries})`)
        setTimeout(() => {
          this.retryLoad()
        }, 2000)
      } else {
        this.error = 'WebRTC视频播放失败，请检查网络连接或视频源'
        this.loading = false
      }
    },
    
    retryLoad() {
      this.error = null
      this.initPlayer()
    },
    
    refreshVideo() {
      this.retryCount = 0
      this.initPlayer()
    },
    
    toggleFullscreen() {
      const videoDom = this.$refs.jswebrtcVideo
      if (videoDom) {
        try {
          if (document.fullscreenElement) {
            document.exitFullscreen()
          } else {
            videoDom.requestFullscreen()
          }
        } catch (error) {
          console.error('全屏操作失败:', error)
        }
      }
    },
    
    closeVideo() {
      this.$emit('close')
    },
    
    destroyPlayer() {
      if (this.player) {
        try {
          this.player.destroy()
          this.player = null
          console.log('JSWebRTC播放器已销毁')
        } catch (error) {
          console.error('销毁JSWebRTC播放器失败:', error)
        }
      }
    },
    
    // 公共方法
    play() {
      const videoDom = this.$refs.jswebrtcVideo
      if (videoDom) {
        videoDom.play().catch(error => {
          console.error('播放失败:', error)
        })
      }
    },
    
    pause() {
      const videoDom = this.$refs.jswebrtcVideo
      if (videoDom) {
        videoDom.pause()
      }
    },
    
    stop() {
      const videoDom = this.$refs.jswebrtcVideo
      if (videoDom) {
        videoDom.pause()
        videoDom.currentTime = 0
      }
    },
    
    setVolume(volume) {
      const videoDom = this.$refs.jswebrtcVideo
      if (videoDom) {
        videoDom.volume = volume
      }
    },
    
    mute() {
      const videoDom = this.$refs.jswebrtcVideo
      if (videoDom) {
        videoDom.muted = true
      }
    },
    
    unmute() {
      const videoDom = this.$refs.jswebrtcVideo
      if (videoDom) {
        videoDom.muted = false
      }
    }
  }
}
</script>

<style scoped>
.jswebrtc-container {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
}

.video-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.video-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  flex: 1;
}

.video-controls {
  display: flex;
  align-items: center;
  gap: 5px;
}

.control-btn {
  padding: 5px;
  font-size: 16px;
  color: #606266;
  transition: color 0.3s;
}

.control-btn:hover {
  color: #409EFF;
}

.close-btn:hover {
  color: #f56c6c;
}

.video-wrapper {
  position: relative;
  background: #000;
  width: 100%;
}

.video-loading,
.video-error {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #fff;
  z-index: 10;
  background: rgba(0, 0, 0, 0.7);
  padding: 20px;
  border-radius: 4px;
  min-width: 150px;
}

.video-loading i,
.video-error i {
  font-size: 24px;
  margin-bottom: 10px;
}

.video-loading span,
.video-error span {
  font-size: 14px;
  margin-bottom: 10px;
  text-align: center;
}

.error-actions {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

.video-error .el-button {
  margin: 0;
}

/* 视频元素样式 */
video {
  display: block;
  background: #000;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .video-header {
    padding: 8px 12px;
  }

  .video-title {
    font-size: 12px;
  }

  .control-btn {
    font-size: 14px;
    padding: 3px;
  }

  .video-loading,
  .video-error {
    padding: 15px;
    min-width: 120px;
  }

  .video-loading i,
  .video-error i {
    font-size: 20px;
  }

  .video-loading span,
  .video-error span {
    font-size: 12px;
  }
}
</style>
