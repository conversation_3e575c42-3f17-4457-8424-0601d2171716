<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JSWebRTC 测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        video {
            width: 100%;
            max-width: 600px;
            height: 300px;
            background: #000;
            margin: 10px 0;
        }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .url-input {
            width: 100%;
            padding: 8px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>JSWebRTC 测试页面</h1>
        
        <div id="status" class="status info">正在检查JSWebRTC库...</div>
        
        <div id="controls" style="display: none;">
            <h3>WebRTC视频播放测试</h3>
            <input 
                type="text" 
                id="videoUrl" 
                class="url-input"
                value="webrtc://live2.sahy.cloud/live/7CTDM3900BM6WS-165-0-7"
                placeholder="输入WebRTC视频URL"
            />
            <button onclick="playVideo()">播放视频</button>
            <button onclick="stopVideo()">停止播放</button>
            
            <video id="testVideo" controls muted>
                您的浏览器不支持视频播放。
            </video>
            
            <div id="videoStatus"></div>
        </div>
        
        <div id="info">
            <h3>测试信息</h3>
            <ul>
                <li>JSWebRTC版本: <span id="jswebrtcVersion">检测中...</span></li>
                <li>浏览器WebRTC支持: <span id="webrtcSupport">检测中...</span></li>
                <li>当前协议: <span id="currentProtocol">检测中...</span></li>
            </ul>
        </div>
        
        <div id="logs">
            <h3>日志信息</h3>
            <div id="logContainer" style="background: #f8f9fa; padding: 10px; border-radius: 4px; height: 200px; overflow-y: auto; font-family: monospace; font-size: 12px;"></div>
            <button onclick="clearLogs()">清除日志</button>
        </div>
    </div>

    <!-- 引入JSWebRTC -->
    <script src="public/js/jswebrtc.min.js"></script>
    
    <script>
        let player = null;
        let logs = [];
        
        function addLog(message) {
            const now = new Date();
            const time = now.toLocaleTimeString();
            const logMessage = `[${time}] ${message}`;
            logs.unshift(logMessage);
            
            // 限制日志数量
            if (logs.length > 100) {
                logs = logs.slice(0, 100);
            }
            
            updateLogDisplay();
            console.log(logMessage);
        }
        
        function updateLogDisplay() {
            const container = document.getElementById('logContainer');
            container.innerHTML = logs.join('\n');
        }
        
        function clearLogs() {
            logs = [];
            updateLogDisplay();
        }
        
        function checkJSWebRTC() {
            const statusDiv = document.getElementById('status');
            const controlsDiv = document.getElementById('controls');
            
            addLog('开始检查JSWebRTC库...');
            
            if (typeof JSWebrtc !== 'undefined') {
                statusDiv.className = 'status success';
                statusDiv.textContent = '✅ JSWebRTC库加载成功！';
                controlsDiv.style.display = 'block';
                
                addLog('JSWebRTC库检测成功');
                document.getElementById('jswebrtcVersion').textContent = 'v1.0 (已加载)';
            } else {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ JSWebRTC库加载失败！请检查文件路径。';
                
                addLog('JSWebRTC库检测失败');
                document.getElementById('jswebrtcVersion').textContent = '未加载';
            }
        }
        
        function checkWebRTCSupport() {
            const supportSpan = document.getElementById('webrtcSupport');
            
            if (typeof RTCPeerConnection !== 'undefined') {
                supportSpan.textContent = '✅ 支持';
                supportSpan.style.color = 'green';
                addLog('浏览器支持WebRTC');
            } else {
                supportSpan.textContent = '❌ 不支持';
                supportSpan.style.color = 'red';
                addLog('浏览器不支持WebRTC');
            }
        }
        
        function checkProtocol() {
            const protocolSpan = document.getElementById('currentProtocol');
            protocolSpan.textContent = window.location.protocol;
            
            if (window.location.protocol === 'https:') {
                protocolSpan.style.color = 'green';
                addLog('当前使用HTTPS协议（推荐）');
            } else {
                protocolSpan.style.color = 'orange';
                addLog('当前使用HTTP协议（WebRTC可能受限）');
            }
        }
        
        function playVideo() {
            const videoUrl = document.getElementById('videoUrl').value;
            const video = document.getElementById('testVideo');
            const statusDiv = document.getElementById('videoStatus');
            
            if (!videoUrl.trim()) {
                alert('请输入视频URL');
                return;
            }
            
            addLog(`开始播放视频: ${videoUrl}`);
            statusDiv.innerHTML = '<p style="color: orange;">正在初始化播放器...</p>';
            
            try {
                // 停止现有播放器
                if (player) {
                    player.destroy();
                    player = null;
                    addLog('销毁现有播放器');
                }
                
                // 创建新的JSWebRTC播放器
                player = new JSWebrtc.Player(videoUrl, {
                    video: video,
                    autoplay: true,
                    onPlay: function(obj) {
                        addLog('播放器开始播放');
                        statusDiv.innerHTML = '<p style="color: green;">✅ 视频播放成功！</p>';
                        
                        // 监听视频事件
                        video.addEventListener('canplay', function() {
                            addLog('视频可以播放');
                            video.play().catch(error => {
                                addLog('自动播放失败: ' + error.message);
                            });
                        });
                        
                        video.addEventListener('play', function() {
                            addLog('视频开始播放');
                        });
                        
                        video.addEventListener('error', function(e) {
                            addLog('视频播放错误: ' + e.message);
                            statusDiv.innerHTML = '<p style="color: red;">❌ 视频播放错误</p>';
                        });
                    },
                    onError: function(error) {
                        addLog('播放器错误: ' + error);
                        statusDiv.innerHTML = '<p style="color: red;">❌ 播放器错误: ' + error + '</p>';
                    }
                });
                
                addLog('JSWebRTC播放器创建成功');
                
            } catch (error) {
                addLog('创建播放器失败: ' + error.message);
                statusDiv.innerHTML = '<p style="color: red;">❌ 创建播放器失败: ' + error.message + '</p>';
            }
        }
        
        function stopVideo() {
            if (player) {
                player.destroy();
                player = null;
                addLog('停止视频播放');
                
                const statusDiv = document.getElementById('videoStatus');
                statusDiv.innerHTML = '<p style="color: gray;">视频已停止</p>';
            }
        }
        
        // 页面加载完成后执行检查
        window.addEventListener('load', function() {
            addLog('页面加载完成');
            
            // 延迟检查，确保脚本加载完成
            setTimeout(function() {
                checkJSWebRTC();
                checkWebRTCSupport();
                checkProtocol();
            }, 100);
        });
        
        // 页面卸载时清理资源
        window.addEventListener('beforeunload', function() {
            if (player) {
                player.destroy();
            }
        });
    </script>
</body>
</html>
