# TCPlayer WebRTC 播放器使用指南

## 🎯 快速开始

### 1. 已完成的集成工作

✅ **TCPlayerVideo组件已创建** (`src/components/TCPlayerVideo.vue`)
✅ **飞控界面已更新** (`src/views/flightmaster/index.vue`)
✅ **演示页面已添加** (`src/views/TCPlayerDemo.vue`)
✅ **路由已配置** (可访问 `/tcplayer-demo`)

### 2. 立即测试

您现在可以：

1. **访问演示页面**：
   ```
   http://localhost:8080/tcplayer-demo
   ```

2. **在飞控界面测试**：
   - 点击设备卡片
   - 查看WebRTC视频播放效果

## 🔧 TCPlayer License 配置

### 重要提醒
TCPlayer 5.0+ 版本**必须**配置License才能正常使用。

### 申请免费License

1. **访问腾讯云控制台**：
   ```
   https://console.cloud.tencent.com/vcube/web?tab=player
   ```

2. **申请基础版License**：
   - 选择"播放器 Web 端基础版 License"
   - 免费申请，有效期1年
   - 支持最多10个域名

3. **获取License URL**：
   申请成功后会得到类似这样的URL：
   ```
   https://license.vod2.myqcloud.com/license/v2/xxx/TXLiveSDK.licence
   ```

### 配置License

在 `src/views/flightmaster/index.vue` 中更新：

```javascript
data() {
  return {
    // 其他数据...
    tcPlayerLicenseUrl: 'https://license.vod2.myqcloud.com/license/v2/xxx/TXLiveSDK.licence'
  }
}
```

## 🚀 功能特性

### ✅ 已实现功能

1. **WebRTC原生支持**
   - 直接播放 `webrtc://` 协议
   - 自动降级到HLS/FLV格式
   - 低延迟实时播放

2. **智能错误处理**
   - 自动重试机制
   - 友好的错误提示
   - 网络异常恢复

3. **动态脚本加载**
   - 按需加载TCPlayer SDK
   - 避免全局依赖冲突
   - 支持CDN加速

4. **完整的播放控制**
   - 播放/暂停/停止
   - 音量控制
   - 全屏支持

## 📋 使用示例

### 基本用法

```vue
<template>
  <TCPlayerVideo
    :src="videoUrl"
    :device-name="deviceName"
    :width="400"
    :height="250"
    :autoplay="false"
    :muted="true"
    :license-url="licenseUrl"
    @close="handleClose"
  />
</template>

<script>
import TCPlayerVideo from '@/components/TCPlayerVideo.vue'

export default {
  components: { TCPlayerVideo },
  data() {
    return {
      videoUrl: 'webrtc://live2.sahy.cloud/live/7CTDM3900BM6WS-165-0-7',
      deviceName: '我的机场',
      licenseUrl: 'your-license-url-here'
    }
  }
}
</script>
```

### 高级用法

```javascript
// 获取播放器实例
const player = this.$refs.tcPlayerVideo

// 控制播放
player.play()      // 播放
player.pause()     // 暂停
player.stop()      // 停止
player.mute()      // 静音
player.unmute()    // 取消静音
player.setVolume(0.5) // 设置音量
```

## 🔄 从VideoPlayer迁移

### 替换步骤

1. **更新import**：
   ```javascript
   // 旧的
   import VideoPlayer from '@/components/VideoPlayer.vue'
   
   // 新的
   import TCPlayerVideo from '@/components/TCPlayerVideo.vue'
   ```

2. **更新组件注册**：
   ```javascript
   components: {
     TCPlayerVideo  // 替换 VideoPlayer
   }
   ```

3. **更新模板**：
   ```vue
   <!-- 旧的 -->
   <VideoPlayer :src="video.src" />
   
   <!-- 新的 -->
   <TCPlayerVideo 
     :src="video.src" 
     :license-url="licenseUrl"
   />
   ```

4. **添加License配置**：
   ```javascript
   data() {
     return {
       licenseUrl: 'your-license-url-here'
     }
   }
   ```

## 🛠 故障排除

### 常见问题

1. **License错误**
   ```
   错误: License验证失败
   解决: 检查licenseUrl是否正确，域名是否已授权
   ```

2. **WebRTC连接失败**
   ```
   错误: WebRTC连接超时
   解决: 检查网络环境，确认服务器支持WebRTC
   ```

3. **脚本加载失败**
   ```
   错误: TCPlayer脚本加载失败
   解决: 检查网络连接，确认CDN可访问
   ```

### 调试技巧

1. **查看控制台日志**：
   ```javascript
   // 组件会输出详细的调试信息
   console.log('TCPlayer初始化状态')
   ```

2. **检查网络请求**：
   - 打开浏览器开发者工具
   - 查看Network标签
   - 确认TCPlayer脚本加载成功

3. **测试不同视频源**：
   ```javascript
   // 测试HLS格式
   videoUrl: 'https://live2.sahy.cloud/live/7CTDM3900BM6WS-165-0-7/index.m3u8'
   
   // 测试FLV格式
   videoUrl: 'https://live2.sahy.cloud/live/7CTDM3900BM6WS-165-0-7.flv'
   ```

## 📊 性能优化

### 建议配置

1. **本地部署TCPlayer资源**：
   - 下载TCPlayer SDK到本地
   - 避免CDN依赖
   - 提高加载速度

2. **License缓存**：
   - 将License URL配置在环境变量中
   - 避免硬编码

3. **视频源优化**：
   - 优先使用WebRTC协议
   - 配置多个备用URL
   - 实现智能切换

## 🎉 总结

TCPlayerVideo组件已经完全集成到飞控界面中，提供了：

- ✅ **WebRTC原生支持**
- ✅ **智能降级机制**
- ✅ **完善的错误处理**
- ✅ **低延迟播放**
- ✅ **广泛的浏览器兼容性**

现在您可以：
1. 申请腾讯云License
2. 配置License URL
3. 测试WebRTC视频播放
4. 享受低延迟的视频体验！

如有问题，请查看演示页面或控制台日志进行调试。
