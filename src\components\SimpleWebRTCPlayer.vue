<template>
  <div class="simple-webrtc-container">
    <div class="video-header">
      <span class="video-title">{{ deviceName }}</span>
      <div class="video-controls">
        <el-button 
          type="text" 
          icon="el-icon-refresh" 
          @click="refreshVideo" 
          class="control-btn"
          title="刷新视频"
        />
        <el-button 
          type="text" 
          icon="el-icon-close" 
          @click="closeVideo" 
          class="control-btn close-btn"
          title="关闭"
        />
      </div>
    </div>
    
    <div class="video-wrapper">
      <!-- 视频容器 -->
      <video
        :id="playerId"
        ref="webrtcVideo"
        controls
        :width="width"
        :height="height"
        :style="{ width: width + 'px', height: height + 'px', objectFit: 'fill' }"
        preload="auto"
        playsinline
        webkit-playsinline
        x5-playsinline
      >
        您的浏览器不支持视频播放。
      </video>
      
      <!-- 加载状态 -->
      <div v-if="loading" class="video-loading">
        <i class="el-icon-loading"></i>
        <span>{{ loadingText }}</span>
        <div class="loading-progress">
          <div class="progress-bar" :style="{ width: progress + '%' }"></div>
        </div>
      </div>
      
      <!-- 错误状态 -->
      <div v-if="error" class="video-error">
        <i class="el-icon-warning"></i>
        <span>{{ error }}</span>
        <div class="error-actions">
          <el-button type="primary" size="mini" @click="retryLoad">重试</el-button>
          <el-button type="success" size="mini" @click="tryAlternativeUrls">尝试其他格式</el-button>
        </div>
      </div>
      
      <!-- WebRTC不支持提示 -->
      <div v-if="!webrtcSupported" class="webrtc-unsupported">
        <i class="el-icon-warning"></i>
        <h4>WebRTC 不支持</h4>
        <p>您的浏览器不支持WebRTC或当前环境不满足WebRTC要求</p>
        <ul>
          <li>请使用现代浏览器（Chrome 56+, Firefox 52+, Safari 11+）</li>
          <li>确保使用HTTPS协议（生产环境）</li>
          <li>检查网络连接和防火墙设置</li>
        </ul>
        <el-button type="primary" size="mini" @click="tryAlternativeUrls">使用其他格式</el-button>
      </div>
    </div>
    
    <!-- 替代URL对话框 -->
    <el-dialog
      title="尝试其他视频格式"
      :visible.sync="showAlternativeDialog"
      width="600px"
      append-to-body
    >
      <div class="alternative-urls">
        <p>WebRTC连接失败，请尝试以下替代格式：</p>
        <div v-for="(urlObj, index) in alternativeUrls" :key="index" class="url-option">
          <div class="url-info">
            <span class="url-type">{{ urlObj.type }}</span>
            <code class="url-text">{{ urlObj.url }}</code>
          </div>
          <el-button size="mini" @click="tryAlternativeUrl(urlObj.url)">尝试播放</el-button>
        </div>
      </div>
      <div slot="footer">
        <el-button @click="showAlternativeDialog = false">关闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'SimpleWebRTCPlayer',
  props: {
    src: {
      type: String,
      required: true
    },
    deviceName: {
      type: String,
      default: '视频播放器'
    },
    width: {
      type: [String, Number],
      default: 400
    },
    height: {
      type: [String, Number],
      default: 250
    },
    autoplay: {
      type: Boolean,
      default: true
    },
    muted: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      player: null,
      playerId: `webrtc-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      loading: false,
      loadingText: '正在初始化...',
      progress: 0,
      error: null,
      retryCount: 0,
      maxRetries: 3,
      webrtcSupported: true,
      showAlternativeDialog: false,
      alternativeUrls: []
    }
  },
  mounted() {
    this.checkWebRTCSupport()
    if (this.webrtcSupported) {
      this.initPlayer()
    }
  },
  beforeDestroy() {
    this.destroyPlayer()
  },
  watch: {
    src(newSrc) {
      if (newSrc && this.webrtcSupported) {
        this.initPlayer()
      }
    }
  },
  methods: {
    checkWebRTCSupport() {
      // 检查WebRTC支持
      this.webrtcSupported = !!(
        window.RTCPeerConnection && 
        window.JSWebrtc &&
        (window.location.protocol === 'https:' || window.location.hostname === 'localhost')
      )
      
      if (!this.webrtcSupported) {
        console.warn('WebRTC不支持或环境不满足要求')
        this.generateAlternativeUrls()
      }
    },
    
    async initPlayer() {
      if (!this.src) {
        this.error = '视频源URL为空'
        return
      }
      
      this.loading = true
      this.error = null
      this.progress = 0
      this.loadingText = '正在连接服务器...'
      
      try {
        // 销毁现有播放器
        this.destroyPlayer()
        
        // 模拟进度
        this.simulateProgress()
        
        // 获取视频DOM元素
        const videoDom = document.getElementById(this.playerId)
        if (!videoDom) {
          throw new Error('视频DOM元素未找到')
        }
        
        console.log('开始初始化WebRTC播放器，URL:', this.src)
        
        // 检查JSWebRTC是否可用
        if (!window.JSWebrtc) {
          throw new Error('JSWebRTC库未加载')
        }
        
        this.loadingText = '正在建立WebRTC连接...'
        
        // 创建JSWebRTC播放器实例
        this.player = new window.JSWebrtc.Player(this.src, {
          video: videoDom,
          autoplay: this.autoplay,
          onPlay: (obj) => {
            console.log('WebRTC播放器开始播放:', obj)
            this.loading = false
            this.progress = 100
            this.retryCount = 0
            this.setupVideoEvents(videoDom)
          },
          onError: (error) => {
            console.error('WebRTC播放器错误:', error)
            this.handleVideoError()
          }
        })
        
        // 设置连接超时
        setTimeout(() => {
          if (this.loading) {
            console.warn('WebRTC连接超时')
            this.handleVideoError('连接超时，请检查网络或服务器状态')
          }
        }, 15000)
        
      } catch (error) {
        console.error('初始化WebRTC播放器失败:', error)
        this.handleVideoError(error.message)
      }
    },
    
    simulateProgress() {
      // 模拟连接进度
      const interval = setInterval(() => {
        if (this.progress < 90 && this.loading) {
          this.progress += Math.random() * 10
        } else {
          clearInterval(interval)
        }
      }, 500)
    },
    
    setupVideoEvents(videoDom) {
      videoDom.addEventListener('canplay', () => {
        console.log('视频可以播放')
        if (this.autoplay) {
          videoDom.play().catch(error => {
            console.warn('自动播放失败:', error)
          })
        }
      })
      
      videoDom.addEventListener('play', () => {
        console.log('视频开始播放')
        this.loading = false
      })
      
      videoDom.addEventListener('error', (e) => {
        console.error('视频播放错误:', e)
        this.handleVideoError('视频播放错误')
      })
    },
    
    handleVideoError(customMessage = null) {
      if (this.retryCount < this.maxRetries) {
        this.retryCount++
        console.log(`WebRTC连接失败，正在重试 (${this.retryCount}/${this.maxRetries})`)
        setTimeout(() => {
          this.retryLoad()
        }, 2000)
      } else {
        this.error = customMessage || 'WebRTC连接失败，请尝试其他格式或检查网络连接'
        this.loading = false
        this.progress = 0
        this.generateAlternativeUrls()
      }
    },
    
    generateAlternativeUrls() {
      if (!this.src) return
      
      try {
        const url = new URL(this.src.replace('webrtc://', 'https://'))
        this.alternativeUrls = [
          {
            type: 'HLS',
            url: `${url.origin}${url.pathname}/index.m3u8`
          },
          {
            type: 'FLV',
            url: `${url.origin}${url.pathname}.flv`
          },
          {
            type: 'MP4',
            url: `${url.origin}${url.pathname}.mp4`
          },
          {
            type: 'HTTP',
            url: this.src.replace('webrtc://', 'https://')
          }
        ]
      } catch (error) {
        console.error('生成替代URL失败:', error)
      }
    },
    
    tryAlternativeUrls() {
      this.generateAlternativeUrls()
      this.showAlternativeDialog = true
    },
    
    tryAlternativeUrl(url) {
      this.showAlternativeDialog = false
      this.$emit('url-change', url)
      this.$message.info(`尝试播放: ${url}`)
    },
    
    retryLoad() {
      this.error = null
      this.initPlayer()
    },
    
    refreshVideo() {
      this.retryCount = 0
      this.initPlayer()
    },
    
    closeVideo() {
      this.$emit('close')
    },
    
    destroyPlayer() {
      if (this.player) {
        try {
          this.player.destroy()
          this.player = null
          console.log('WebRTC播放器已销毁')
        } catch (error) {
          console.error('销毁WebRTC播放器失败:', error)
        }
      }
    }
  }
}
</script>

<style scoped>
.simple-webrtc-container {
  background: #fff;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  overflow: hidden;
  position: relative;
}

.video-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background: #f5f7fa;
  border-bottom: 1px solid #e4e7ed;
}

.video-title {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  flex: 1;
}

.video-controls {
  display: flex;
  align-items: center;
  gap: 5px;
}

.control-btn {
  padding: 5px;
  font-size: 16px;
  color: #606266;
  transition: color 0.3s;
}

.control-btn:hover {
  color: #409EFF;
}

.close-btn:hover {
  color: #f56c6c;
}

.video-wrapper {
  position: relative;
  background: #000;
  width: 100%;
}

.video-loading,
.video-error,
.webrtc-unsupported {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #fff;
  z-index: 10;
  background: rgba(0, 0, 0, 0.8);
  padding: 20px;
  border-radius: 4px;
  min-width: 200px;
  text-align: center;
}

.video-loading i,
.video-error i,
.webrtc-unsupported i {
  font-size: 24px;
  margin-bottom: 10px;
}

.loading-progress {
  width: 200px;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  margin-top: 10px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: #409EFF;
  transition: width 0.3s ease;
}

.webrtc-unsupported h4 {
  margin: 10px 0;
  color: #f56c6c;
}

.webrtc-unsupported ul {
  text-align: left;
  margin: 10px 0;
  padding-left: 20px;
}

.webrtc-unsupported li {
  margin: 5px 0;
  font-size: 12px;
}

.error-actions {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

/* 替代URL对话框样式 */
.alternative-urls {
  max-height: 400px;
  overflow-y: auto;
}

.url-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px;
  margin: 8px 0;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: #f9f9f9;
}

.url-info {
  flex: 1;
  margin-right: 10px;
}

.url-type {
  display: inline-block;
  background: #409EFF;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  margin-right: 10px;
  min-width: 50px;
  text-align: center;
}

.url-text {
  font-family: monospace;
  font-size: 12px;
  color: #606266;
  word-break: break-all;
  background: #f5f7fa;
  padding: 4px 8px;
  border-radius: 4px;
  display: block;
  margin-top: 5px;
}
</style>
