<template>
  <div class="jswebrtc-demo">
    <div class="demo-header">
      <h1>JSWebRTC 播放器演示</h1>
      <p>基于JSWebRTC库的WebRTC视频播放解决方案</p>
    </div>
    
    <div class="demo-controls">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form label-width="100px">
            <el-form-item label="视频源URL:">
              <el-input 
                v-model="videoUrl" 
                placeholder="输入WebRTC视频URL"
                @keyup.enter="updateVideoSrc"
              />
            </el-form-item>
            <el-form-item label="设备名称:">
              <el-input v-model="deviceName" placeholder="输入设备名称" />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="updateVideoSrc">更新视频源</el-button>
              <el-button @click="resetDemo">重置演示</el-button>
            </el-form-item>
          </el-form>
        </el-col>
        
        <el-col :span="12">
          <div class="demo-info">
            <h3>预设视频源</h3>
            <div class="preset-urls">
              <div 
                v-for="(preset, index) in presetUrls" 
                :key="index"
                class="preset-item"
                @click="selectPreset(preset)"
              >
                <div class="preset-type">{{ preset.type }}</div>
                <div class="preset-url">{{ preset.url }}</div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    
    <div class="demo-player">
      <h3>视频播放器</h3>
      <div class="player-wrapper">
        <JSWebRTCPlayer
          v-if="currentVideoSrc"
          ref="jswebrtcPlayer"
          :src="currentVideoSrc"
          :device-name="deviceName"
          :width="800"
          :height="450"
          :autoplay="true"
          :muted="true"
          @close="handleVideoClose"
        />
        <div v-else class="no-video">
          <i class="el-icon-video-camera"></i>
          <p>请输入视频源URL开始播放</p>
        </div>
      </div>
    </div>
    
    <div class="demo-controls-panel">
      <h3>播放器控制</h3>
      <div class="control-buttons">
        <el-button @click="playVideo" :disabled="!currentVideoSrc">播放</el-button>
        <el-button @click="pauseVideo" :disabled="!currentVideoSrc">暂停</el-button>
        <el-button @click="stopVideo" :disabled="!currentVideoSrc">停止</el-button>
        <el-button @click="muteVideo" :disabled="!currentVideoSrc">静音</el-button>
        <el-button @click="unmuteVideo" :disabled="!currentVideoSrc">取消静音</el-button>
      </div>
      
      <div class="volume-control">
        <span>音量: </span>
        <el-slider 
          v-model="volume" 
          :min="0" 
          :max="1" 
          :step="0.1"
          :disabled="!currentVideoSrc"
          @change="setVolume"
          style="width: 200px; margin-left: 10px;"
        />
        <span style="margin-left: 10px;">{{ Math.round(volume * 100) }}%</span>
      </div>
    </div>
    
    <div class="demo-logs">
      <h3>事件日志</h3>
      <div class="log-container">
        <div v-for="(log, index) in logs" :key="index" class="log-item">
          <span class="log-time">{{ log.time }}</span>
          <span class="log-message">{{ log.message }}</span>
        </div>
      </div>
      <el-button size="mini" @click="clearLogs">清除日志</el-button>
    </div>
    
    <div class="demo-info-section">
      <h3>JSWebRTC 说明</h3>
      <div class="info-content">
        <el-alert
          title="JSWebRTC 特性"
          type="info"
          :closable="false"
          show-icon
        >
          <ul>
            <li>✅ 原生支持WebRTC协议</li>
            <li>✅ 超低延迟实时播放</li>
            <li>✅ 轻量级，无需额外依赖</li>
            <li>✅ 支持SRS流媒体服务器</li>
            <li>✅ 简单易用的API</li>
          </ul>
        </el-alert>
        
        <el-alert
          title="使用要求"
          type="warning"
          :closable="false"
          show-icon
          style="margin-top: 10px;"
        >
          <ul>
            <li>⚠️ 需要HTTPS环境（生产环境）</li>
            <li>⚠️ 需要WebRTC兼容的浏览器</li>
            <li>⚠️ 需要配置STUN/TURN服务器</li>
            <li>⚠️ 视频源必须支持WebRTC协议</li>
          </ul>
        </el-alert>
      </div>
    </div>
  </div>
</template>

<script>
import JSWebRTCPlayer from '@/components/JSWebRTCPlayer.vue'

export default {
  name: 'JSWebRTCDemo',
  components: {
    JSWebRTCPlayer
  },
  data() {
    return {
      videoUrl: 'webrtc://live2.sahy.cloud/live/7CTDM3900BM6WS-165-0-7',
      currentVideoSrc: '',
      deviceName: '我的机场',
      volume: 0.5,
      logs: [],
      
      presetUrls: [
        {
          type: 'WebRTC',
          url: 'webrtc://live2.sahy.cloud/live/7CTDM3900BM6WS-165-0-7'
        },
        {
          type: 'WebRTC Demo',
          url: 'webrtc://r.ossrs.net/live/livestream'
        },
        {
          type: 'WebRTC Test',
          url: 'webrtc://demo.ossrs.net/live/livestream'
        }
      ]
    }
  },
  mounted() {
    this.addLog('JSWebRTC演示页面加载完成')
    // 默认加载第一个视频源
    this.updateVideoSrc()
  },
  methods: {
    updateVideoSrc() {
      if (!this.videoUrl.trim()) {
        this.$message.warning('请输入视频源URL')
        return
      }
      
      this.currentVideoSrc = this.videoUrl.trim()
      this.addLog(`更新视频源: ${this.currentVideoSrc}`)
    },
    
    selectPreset(preset) {
      this.videoUrl = preset.url
      this.updateVideoSrc()
      this.addLog(`选择预设视频源: ${preset.type}`)
    },
    
    resetDemo() {
      this.videoUrl = 'webrtc://live2.sahy.cloud/live/7CTDM3900BM6WS-165-0-7'
      this.deviceName = '我的机场'
      this.volume = 0.5
      this.currentVideoSrc = ''
      this.clearLogs()
      this.addLog('演示重置完成')
    },
    
    // 播放器控制方法
    playVideo() {
      if (this.$refs.jswebrtcPlayer) {
        this.$refs.jswebrtcPlayer.play()
        this.addLog('播放视频')
      }
    },
    
    pauseVideo() {
      if (this.$refs.jswebrtcPlayer) {
        this.$refs.jswebrtcPlayer.pause()
        this.addLog('暂停视频')
      }
    },
    
    stopVideo() {
      if (this.$refs.jswebrtcPlayer) {
        this.$refs.jswebrtcPlayer.stop()
        this.addLog('停止视频')
      }
    },
    
    muteVideo() {
      if (this.$refs.jswebrtcPlayer) {
        this.$refs.jswebrtcPlayer.mute()
        this.addLog('视频静音')
      }
    },
    
    unmuteVideo() {
      if (this.$refs.jswebrtcPlayer) {
        this.$refs.jswebrtcPlayer.unmute()
        this.addLog('取消视频静音')
      }
    },
    
    setVolume(value) {
      if (this.$refs.jswebrtcPlayer) {
        this.$refs.jswebrtcPlayer.setVolume(value)
        this.addLog(`设置音量: ${Math.round(value * 100)}%`)
      }
    },
    
    handleVideoClose() {
      this.currentVideoSrc = ''
      this.addLog('视频播放器关闭')
    },
    
    // 日志管理
    addLog(message) {
      const now = new Date()
      const time = now.toLocaleTimeString()
      this.logs.unshift({
        time,
        message
      })
      
      // 限制日志数量
      if (this.logs.length > 50) {
        this.logs = this.logs.slice(0, 50)
      }
    },
    
    clearLogs() {
      this.logs = []
    }
  }
}
</script>

<style scoped>
.jswebrtc-demo {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-header {
  text-align: center;
  margin-bottom: 30px;
}

.demo-header h1 {
  color: #303133;
  margin-bottom: 10px;
}

.demo-header p {
  color: #606266;
  font-size: 14px;
}

.demo-controls {
  margin-bottom: 30px;
}

.demo-info h3 {
  margin-bottom: 15px;
  color: #303133;
}

.preset-urls {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
}

.preset-item {
  padding: 10px;
  border-bottom: 1px solid #e4e7ed;
  cursor: pointer;
  transition: background-color 0.3s;
}

.preset-item:last-child {
  border-bottom: none;
}

.preset-item:hover {
  background-color: #f5f7fa;
}

.preset-type {
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 5px;
}

.preset-url {
  font-size: 12px;
  color: #606266;
  word-break: break-all;
}

.demo-player {
  margin-bottom: 30px;
}

.demo-player h3 {
  margin-bottom: 15px;
  color: #303133;
}

.player-wrapper {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
  background: #f5f7fa;
}

.no-video {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 450px;
  color: #909399;
}

.no-video i {
  font-size: 48px;
  margin-bottom: 10px;
}

.demo-controls-panel {
  margin-bottom: 30px;
}

.demo-controls-panel h3 {
  margin-bottom: 15px;
  color: #303133;
}

.control-buttons {
  margin-bottom: 20px;
}

.control-buttons .el-button {
  margin-right: 10px;
  margin-bottom: 10px;
}

.volume-control {
  display: flex;
  align-items: center;
}

.demo-logs {
  margin-bottom: 30px;
}

.demo-logs h3 {
  margin-bottom: 15px;
  color: #303133;
}

.log-container {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  height: 200px;
  overflow-y: auto;
  background: #fafafa;
  padding: 10px;
  margin-bottom: 10px;
}

.log-item {
  margin-bottom: 5px;
  font-size: 12px;
}

.log-time {
  color: #909399;
  margin-right: 10px;
}

.log-message {
  color: #303133;
}

.demo-info-section {
  margin-bottom: 30px;
}

.demo-info-section h3 {
  margin-bottom: 15px;
  color: #303133;
}

.info-content ul {
  margin: 0;
  padding-left: 20px;
}

.info-content li {
  margin-bottom: 5px;
}

/* 响应式设计 */
@media screen and (max-width: 768px) {
  .jswebrtc-demo {
    padding: 10px;
  }
  
  .player-wrapper {
    width: 100%;
  }
  
  .control-buttons .el-button {
    width: 100%;
    margin-right: 0;
  }
  
  .volume-control {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .volume-control .el-slider {
    width: 100% !important;
    margin: 10px 0 !important;
  }
}
</style>
