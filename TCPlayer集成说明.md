# TCPlayer WebRTC 播放器集成说明

## 概述

基于腾讯云TCPlayer SDK创建的WebRTC视频播放器组件，专门用于解决WebRTC协议视频播放问题。

## 主要特性

### ✅ **WebRTC原生支持**
- 直接支持 `webrtc://` 协议
- 自动降级到HLS/FLV格式
- 低延迟实时播放

### ✅ **多协议兼容**
- WebRTC (主要)
- HLS (M3U8)
- FLV
- MP4
- RTMP

### ✅ **智能降级**
- WebRTC连接失败时自动尝试其他格式
- 提供多个备用URL
- 保证播放成功率

## 使用方法

### 1. 替换现有组件

在需要播放WebRTC视频的地方，将 `VideoPlayer` 替换为 `TCPlayerVideo`：

```vue
<template>
  <div class="video-containers-wrapper">
    <div v-for="(video, index) in videos" :key="index" class="video-container">
      <!-- 使用新的TCPlayer组件 -->
      <TCPlayerVideo
        :src="video.src"
        :device-name="video.deviceName"
        :width="400"
        :height="250"
        :autoplay="false"
        :muted="true"
        :license-url="tcPlayerLicenseUrl"
        @close="closeVideo(index)"
      />
    </div>
  </div>
</template>

<script>
import TCPlayerVideo from '@/components/TCPlayerVideo.vue'

export default {
  components: {
    TCPlayerVideo
  },
  data() {
    return {
      // TCPlayer License URL (需要申请)
      tcPlayerLicenseUrl: 'your-license-url-here',
      videos: [
        {
          src: 'webrtc://live2.sahy.cloud/live/7CTDM3900BM6WS-165-0-7',
          deviceName: '我的机场'
        }
      ]
    }
  }
}
</script>
```

### 2. 申请TCPlayer License

**重要**: TCPlayer 5.0+ 版本需要License才能使用。

#### 免费基础版License
1. 访问 [腾讯云视立方控制台](https://console.cloud.tencent.com/vcube/web?tab=player)
2. 申请"播放器 Web 端基础版 License"
3. 获取 `licenseUrl`

#### License配置
```javascript
// 在组件中配置License
<TCPlayerVideo
  :license-url="'https://license.vod2.myqcloud.com/license/v2/xxx/TXLiveSDK.licence'"
  // ... 其他属性
/>
```

### 3. 组件属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `src` | String | - | 视频源URL (必需) |
| `deviceName` | String | '视频播放器' | 设备名称 |
| `width` | Number/String | 400 | 播放器宽度 |
| `height` | Number/String | 250 | 播放器高度 |
| `autoplay` | Boolean | false | 自动播放 |
| `muted` | Boolean | true | 静音播放 |
| `licenseUrl` | String | '' | TCPlayer License URL |

### 4. 组件事件

| 事件 | 说明 |
|------|------|
| `@close` | 关闭播放器 |

### 5. 公共方法

```javascript
// 获取组件引用
this.$refs.tcPlayerVideo.play()      // 播放
this.$refs.tcPlayerVideo.pause()     // 暂停
this.$refs.tcPlayerVideo.stop()      // 停止
this.$refs.tcPlayerVideo.mute()      // 静音
this.$refs.tcPlayerVideo.unmute()    // 取消静音
this.$refs.tcPlayerVideo.setVolume(0.5) // 设置音量
```

## WebRTC降级策略

当WebRTC连接失败时，组件会自动尝试以下格式：

```
原始URL: webrtc://live2.sahy.cloud/live/7CTDM3900BM6WS-165-0-7

降级URL:
1. https://live2.sahy.cloud/live/7CTDM3900BM6WS-165-0-7.flv
2. https://live2.sahy.cloud/live/7CTDM3900BM6WS-165-0-7/index.m3u8
3. https://live2.sahy.cloud/live/7CTDM3900BM6WS-165-0-7.m3u8
```

## 优势对比

| 特性 | 原VideoPlayer | TCPlayerVideo |
|------|---------------|---------------|
| WebRTC支持 | ❌ 不支持 | ✅ 原生支持 |
| 自动降级 | ❌ 无 | ✅ 智能降级 |
| 低延迟 | ❌ 普通 | ✅ 超低延迟 |
| 错误处理 | ⚠️ 基础 | ✅ 完善 |
| 浏览器兼容 | ⚠️ 有限 | ✅ 广泛支持 |

## 注意事项

### 1. License申请
- 基础版License免费，有效期1年
- 支持最多10个精准域名
- 本地开发(localhost)无需License

### 2. 网络要求
- WebRTC需要良好的网络环境
- 建议使用HTTPS协议
- 可能需要配置STUN/TURN服务器

### 3. 浏览器支持
- Chrome 56+
- Firefox 52+
- Safari 11+
- Edge 79+

## 故障排除

### 1. License错误
```
错误: License验证失败
解决: 检查licenseUrl是否正确，域名是否已授权
```

### 2. WebRTC连接失败
```
错误: WebRTC连接超时
解决: 检查网络环境，确认服务器支持WebRTC
```

### 3. 脚本加载失败
```
错误: TCPlayer脚本加载失败
解决: 检查网络连接，确认CDN可访问
```

## 完整示例

```vue
<template>
  <div class="flight-control">
    <TCPlayerVideo
      :src="videoSrc"
      :device-name="deviceName"
      :width="600"
      :height="400"
      :autoplay="true"
      :muted="false"
      :license-url="licenseUrl"
      @close="handleVideoClose"
    />
  </div>
</template>

<script>
import TCPlayerVideo from '@/components/TCPlayerVideo.vue'

export default {
  name: 'FlightControl',
  components: {
    TCPlayerVideo
  },
  data() {
    return {
      videoSrc: 'webrtc://live2.sahy.cloud/live/7CTDM3900BM6WS-165-0-7',
      deviceName: '我的机场',
      licenseUrl: 'https://license.vod2.myqcloud.com/license/v2/xxx/TXLiveSDK.licence'
    }
  },
  methods: {
    handleVideoClose() {
      console.log('视频播放器关闭')
    }
  }
}
</script>
```

## 总结

TCPlayerVideo组件提供了完整的WebRTC播放解决方案，解决了原有播放器的所有问题：

- ✅ 原生WebRTC支持
- ✅ 智能降级机制  
- ✅ 完善的错误处理
- ✅ 低延迟播放
- ✅ 广泛的浏览器兼容性

建议在飞控界面中全面替换为TCPlayerVideo组件。
