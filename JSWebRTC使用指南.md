# JSWebRTC WebRTC 播放器使用指南

## 🎯 概述

基于JSWebRTC库实现的WebRTC视频播放器组件，专门用于播放WebRTC协议的实时视频流。

## 📦 已完成的集成工作

### ✅ **文件结构**
```
src/
├── components/
│   └── JSWebRTCPlayer.vue          # JSWebRTC播放器组件
├── views/
│   ├── flightmaster/index.vue      # 飞控界面（已更新）
│   └── JSWebRTCDemo.vue           # JSWebRTC演示页面
└── utils/dict/
    └── jswebrtc.min.js            # JSWebRTC库文件

public/
├── js/
│   └── jswebrtc.min.js            # 全局JSWebRTC库
└── index.html                     # 已引入JSWebRTC脚本
```

### ✅ **路由配置**
- 演示页面：`/jswebrtc-demo`
- 飞控界面已更新为使用JSWebRTCPlayer

## 🚀 快速开始

### 1. 立即测试

您现在可以：

1. **访问演示页面**：
   ```
   http://localhost:8080/jswebrtc-demo
   ```

2. **在飞控界面测试**：
   - 点击设备卡片
   - 查看WebRTC视频播放效果

### 2. 基本使用

```vue
<template>
  <JSWebRTCPlayer
    :src="videoUrl"
    :device-name="deviceName"
    :width="400"
    :height="250"
    :autoplay="true"
    :muted="true"
    @close="handleClose"
  />
</template>

<script>
import JSWebRTCPlayer from '@/components/JSWebRTCPlayer.vue'

export default {
  components: { JSWebRTCPlayer },
  data() {
    return {
      videoUrl: 'webrtc://live2.sahy.cloud/live/7CTDM3900BM6WS-165-0-7',
      deviceName: '我的机场'
    }
  }
}
</script>
```

## 🔧 组件属性

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `src` | String | - | WebRTC视频源URL (必需) |
| `deviceName` | String | '视频播放器' | 设备名称 |
| `width` | Number/String | 400 | 播放器宽度 |
| `height` | Number/String | 250 | 播放器高度 |
| `autoplay` | Boolean | true | 自动播放 |
| `muted` | Boolean | true | 静音播放 |

## 📋 组件事件

| 事件 | 说明 |
|------|------|
| `@close` | 关闭播放器 |

## 🎮 公共方法

```javascript
// 获取组件引用
const player = this.$refs.jswebrtcPlayer

// 播放控制
player.play()           // 播放
player.pause()          // 暂停
player.stop()           // 停止
player.mute()           // 静音
player.unmute()         // 取消静音
player.setVolume(0.5)   // 设置音量 (0-1)
```

## 🌟 JSWebRTC 特性

### ✅ **核心优势**

1. **原生WebRTC支持**
   - 直接支持 `webrtc://` 协议
   - 无需额外转换或代理

2. **超低延迟**
   - 实时流媒体播放
   - 延迟通常 < 500ms

3. **轻量级**
   - 库文件仅 ~10KB
   - 无额外依赖

4. **SRS兼容**
   - 专门支持SRS流媒体服务器
   - 完美集成WebRTC功能

### ⚡ **技术原理**

```javascript
// JSWebRTC工作流程
1. 解析WebRTC URL
2. 创建RTCPeerConnection
3. 生成SDP Offer
4. 发送到SRS服务器
5. 接收SDP Answer
6. 建立WebRTC连接
7. 播放实时视频流
```

## 🔄 从其他播放器迁移

### 从VideoPlayer迁移

```javascript
// 旧的VideoPlayer
import VideoPlayer from '@/components/VideoPlayer.vue'

// 新的JSWebRTCPlayer
import JSWebRTCPlayer from '@/components/JSWebRTCPlayer.vue'
```

```vue
<!-- 旧的模板 -->
<VideoPlayer :src="video.src" />

<!-- 新的模板 -->
<JSWebRTCPlayer :src="video.src" />
```

### 从TCPlayerVideo迁移

```javascript
// 移除TCPlayer相关配置
// 不再需要licenseUrl等配置

// 直接使用JSWebRTCPlayer
<JSWebRTCPlayer :src="webrtcUrl" />
```

## 🛠 故障排除

### 常见问题

1. **JSWebRTC未定义**
   ```
   错误: JSWebRTC is not defined
   解决: 检查public/js/jswebrtc.min.js是否存在
        确认index.html中已引入脚本
   ```

2. **WebRTC连接失败**
   ```
   错误: WebRTC连接超时
   解决: 检查视频源URL是否正确
        确认服务器支持WebRTC协议
        检查网络环境和防火墙设置
   ```

3. **视频无法播放**
   ```
   错误: 视频元素无法播放
   解决: 检查浏览器是否支持WebRTC
        确认HTTPS环境（生产环境必需）
        检查音频权限设置
   ```

### 调试技巧

1. **查看控制台日志**：
   ```javascript
   // 组件会输出详细的调试信息
   console.log('JSWebRTC初始化状态')
   console.log('offer:', offer)
   console.log('answer:', answer)
   ```

2. **检查WebRTC连接**：
   ```javascript
   // 在浏览器开发者工具中
   // 查看Network标签的WebSocket连接
   // 查看Console标签的WebRTC日志
   ```

3. **测试不同视频源**：
   ```javascript
   // 测试官方演示源
   'webrtc://r.ossrs.net/live/livestream'
   
   // 测试您的视频源
   'webrtc://live2.sahy.cloud/live/7CTDM3900BM6WS-165-0-7'
   ```

## 📊 性能优化

### 建议配置

1. **网络优化**：
   - 使用HTTPS协议
   - 配置STUN/TURN服务器
   - 优化网络带宽

2. **播放器优化**：
   - 启用自动播放
   - 设置合适的分辨率
   - 使用静音播放避免浏览器限制

3. **服务器优化**：
   - 使用SRS最新版本
   - 配置WebRTC相关参数
   - 优化服务器性能

## 🌐 浏览器兼容性

| 浏览器 | 版本要求 | WebRTC支持 |
|--------|----------|------------|
| Chrome | 56+ | ✅ 完全支持 |
| Firefox | 52+ | ✅ 完全支持 |
| Safari | 11+ | ✅ 支持 |
| Edge | 79+ | ✅ 完全支持 |
| IE | - | ❌ 不支持 |

## 📚 参考资源

1. **JSWebRTC GitHub**：
   ```
   https://github.com/kernelj/jswebrtc
   ```

2. **SRS官方文档**：
   ```
   https://ossrs.net/lts/zh-cn/docs/v4/doc/webrtc
   ```

3. **WebRTC标准**：
   ```
   https://webrtc.org/
   ```

## 🎉 总结

JSWebRTCPlayer组件提供了完整的WebRTC播放解决方案：

- ✅ **原生WebRTC支持**
- ✅ **超低延迟播放**
- ✅ **轻量级实现**
- ✅ **简单易用的API**
- ✅ **完善的错误处理**

现在您可以：
1. 在飞控界面中播放WebRTC视频
2. 享受超低延迟的实时视频体验
3. 使用简单的API控制播放器
4. 获得稳定可靠的播放效果

如有问题，请查看演示页面或控制台日志进行调试。
