<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TCPlayer License 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #005a87;
        }
        .url-input {
            width: 100%;
            padding: 8px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            background: #f8f9fa;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>TCPlayer License 测试工具</h1>
        
        <div class="info">
            <strong>说明：</strong>此工具用于测试TCPlayer License URL的有效性
        </div>
        
        <h3>License URL 测试</h3>
        <input 
            type="text" 
            id="licenseUrl" 
            class="url-input"
            value="https://license.vod2.myqcloud.com/license/v2/1303164718_1/v_cube.license"
            placeholder="输入TCPlayer License URL"
        />
        <button onclick="testLicense()">测试License</button>
        <button onclick="testCurrentLicense()">测试当前配置的License</button>
        
        <div id="licenseResult"></div>
        
        <h3>环境检查</h3>
        <div id="environmentCheck">
            <div class="test-result">
                <strong>当前域名：</strong> <span id="currentDomain"></span>
            </div>
            <div class="test-result">
                <strong>协议：</strong> <span id="currentProtocol"></span>
            </div>
            <div class="test-result">
                <strong>是否本地环境：</strong> <span id="isLocalhost"></span>
            </div>
        </div>
        
        <h3>License 申请指南</h3>
        <div class="warning">
            <p><strong>如何申请免费License：</strong></p>
            <ol>
                <li>访问 <a href="https://console.cloud.tencent.com/vcube/web?tab=player" target="_blank">腾讯云控制台</a></li>
                <li>选择"播放器 Web 端基础版 License"</li>
                <li>点击"免费申请"</li>
                <li>填写应用信息，添加域名（包括localhost用于开发）</li>
                <li>获取License URL并配置到项目中</li>
            </ol>
        </div>
        
        <h3>常见错误码</h3>
        <div class="test-result">
            <ul>
                <li><strong>错误码53：</strong> License时间验证失败（License已过期）</li>
                <li><strong>错误码54：</strong> License类型错误</li>
                <li><strong>错误码55：</strong> 缺少License URL</li>
                <li><strong>错误码56：</strong> License域名验证失败</li>
            </ul>
        </div>
        
        <h3>配置示例</h3>
        <div class="test-result">
            <pre><code>// 在 Vue 组件中配置
data() {
  return {
    tcPlayerLicenseUrl: 'https://license.vod2.myqcloud.com/license/v2/xxx/v_cube.license'
  }
}</code></pre>
        </div>
    </div>

    <script>
        // 页面加载时执行环境检查
        window.addEventListener('load', function() {
            checkEnvironment();
        });
        
        function checkEnvironment() {
            const domain = window.location.hostname;
            const protocol = window.location.protocol;
            const isLocal = domain === 'localhost' || domain === '127.0.0.1' || domain === '0.0.0.0';
            
            document.getElementById('currentDomain').textContent = domain;
            document.getElementById('currentProtocol').textContent = protocol;
            document.getElementById('isLocalhost').textContent = isLocal ? '是（开发环境）' : '否（生产环境）';
            
            // 根据环境给出建议
            if (isLocal) {
                document.getElementById('isLocalhost').style.color = 'green';
            } else {
                document.getElementById('isLocalhost').style.color = 'orange';
            }
        }
        
        function testLicense() {
            const licenseUrl = document.getElementById('licenseUrl').value;
            const resultDiv = document.getElementById('licenseResult');
            
            if (!licenseUrl.trim()) {
                resultDiv.innerHTML = '<div class="error">请输入License URL</div>';
                return;
            }
            
            resultDiv.innerHTML = '<div class="info">正在测试License URL...</div>';
            
            // 测试License URL是否可访问
            fetch(licenseUrl, {
                method: 'HEAD',
                mode: 'no-cors'
            })
            .then(response => {
                console.log('License URL响应:', response);
                resultDiv.innerHTML = `
                    <div class="success">
                        <strong>✅ License URL可访问</strong><br>
                        URL: ${licenseUrl}<br>
                        状态: 服务器响应正常
                    </div>
                    <div class="warning">
                        <strong>注意：</strong>URL可访问不代表License有效，还需要检查：<br>
                        1. License是否过期<br>
                        2. 当前域名是否在授权列表中<br>
                        3. License类型是否正确
                    </div>
                `;
            })
            .catch(error => {
                console.error('License URL测试失败:', error);
                resultDiv.innerHTML = `
                    <div class="error">
                        <strong>❌ License URL测试失败</strong><br>
                        URL: ${licenseUrl}<br>
                        错误: ${error.message}<br>
                        可能原因：<br>
                        1. URL不存在或已失效<br>
                        2. 网络连接问题<br>
                        3. CORS策略限制
                    </div>
                    <div class="info">
                        <strong>建议：</strong><br>
                        1. 检查URL是否正确<br>
                        2. 重新申请License<br>
                        3. 联系腾讯云技术支持
                    </div>
                `;
            });
        }
        
        function testCurrentLicense() {
            // 测试项目中当前配置的License
            const currentLicense = 'https://license.vod2.myqcloud.com/license/v2/1303164718_1/v_cube.license';
            document.getElementById('licenseUrl').value = currentLicense;
            testLicense();
        }
        
        // 添加一些实用的检查函数
        function checkDomainInLicense() {
            alert('此功能需要在实际的TCPlayer环境中测试，请在Vue项目中查看控制台日志');
        }
        
        function generateLicenseConfig() {
            const licenseUrl = document.getElementById('licenseUrl').value;
            if (!licenseUrl.trim()) {
                alert('请先输入License URL');
                return;
            }
            
            const config = `
// Vue组件配置
data() {
  return {
    tcPlayerLicenseUrl: '${licenseUrl}'
  }
}

// 或者使用环境变量
// .env.production
VUE_APP_TCPLAYER_LICENSE_URL=${licenseUrl}

// 组件中使用
tcPlayerLicenseUrl: process.env.VUE_APP_TCPLAYER_LICENSE_URL || ''
            `;
            
            navigator.clipboard.writeText(config).then(() => {
                alert('配置代码已复制到剪贴板');
            }).catch(() => {
                prompt('请复制以下配置代码:', config);
            });
        }
    </script>
</body>
</html>
