<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebRTC URL 测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .url-test {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: #f9f9f9;
        }
        .url {
            font-family: monospace;
            color: #333;
            word-break: break-all;
        }
        .status {
            margin-top: 5px;
            font-weight: bold;
        }
        .success { color: green; }
        .error { color: red; }
        .testing { color: orange; }
        button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background: #005a87;
        }
        video {
            width: 100%;
            max-width: 600px;
            height: 300px;
            background: #000;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WebRTC URL 转换测试</h1>
        <p>原始WebRTC URL: <code>webrtc://live2.sahy.cloud/live/7CTDM3900BM6WS-165-0-7</code></p>
        
        <button onclick="testAllUrls()">测试所有URL</button>
        <button onclick="clearResults()">清除结果</button>
        
        <div id="results"></div>
        
        <h2>视频播放测试</h2>
        <video id="testVideo" controls muted>
            您的浏览器不支持视频播放。
        </video>
        <div id="videoStatus"></div>
    </div>

    <script>
        const originalUrl = 'webrtc://live2.sahy.cloud/live/7CTDM3900BM6WS-165-0-7';
        
        function generatePossibleUrls(webrtcUrl) {
            try {
                const url = new URL(webrtcUrl.replace('webrtc://', 'https://'));
                
                return [
                    // HLS格式
                    { url: `${url.origin}${url.pathname}/index.m3u8`, type: 'HLS' },
                    { url: `${url.origin}${url.pathname}.m3u8`, type: 'HLS' },
                    { url: `${url.origin}${url.pathname}/playlist.m3u8`, type: 'HLS' },
                    
                    // FLV格式
                    { url: `${url.origin}${url.pathname}.flv`, type: 'FLV' },
                    { url: `${url.origin}${url.pathname}/index.flv`, type: 'FLV' },
                    
                    // MP4格式
                    { url: `${url.origin}${url.pathname}.mp4`, type: 'MP4' },
                    
                    // HTTP直播流
                    { url: `${url.origin}${url.pathname}`, type: 'HTTP' },
                    
                    // 其他协议
                    { url: webrtcUrl.replace('webrtc://', 'http://'), type: 'HTTP' },
                    { url: webrtcUrl.replace('webrtc://', 'https://'), type: 'HTTPS' }
                ];
            } catch (error) {
                console.error('生成URL失败:', error);
                return [];
            }
        }
        
        async function testUrl(urlObj) {
            const { url, type } = urlObj;
            
            return new Promise((resolve) => {
                const img = new Image();
                const timeout = setTimeout(() => {
                    resolve({ url, type, status: 'timeout', message: '请求超时' });
                }, 5000);
                
                // 对于视频URL，我们使用fetch来测试
                fetch(url, { 
                    method: 'HEAD',
                    mode: 'no-cors' // 避免CORS问题
                })
                .then(response => {
                    clearTimeout(timeout);
                    resolve({ 
                        url, 
                        type, 
                        status: 'success', 
                        message: `状态码: ${response.status || '未知'}` 
                    });
                })
                .catch(error => {
                    clearTimeout(timeout);
                    resolve({ 
                        url, 
                        type, 
                        status: 'error', 
                        message: error.message 
                    });
                });
            });
        }
        
        async function testAllUrls() {
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML = '<h2>测试结果</h2>';
            
            const possibleUrls = generatePossibleUrls(originalUrl);
            
            for (const urlObj of possibleUrls) {
                const testDiv = document.createElement('div');
                testDiv.className = 'url-test';
                testDiv.innerHTML = `
                    <div class="url">${urlObj.url}</div>
                    <div class="status testing">测试中... (${urlObj.type})</div>
                `;
                resultsDiv.appendChild(testDiv);
                
                const result = await testUrl(urlObj);
                const statusDiv = testDiv.querySelector('.status');
                statusDiv.className = `status ${result.status}`;
                statusDiv.textContent = `${result.status.toUpperCase()}: ${result.message} (${result.type})`;
                
                // 如果成功，尝试在视频元素中播放
                if (result.status === 'success') {
                    tryPlayVideo(result.url);
                }
            }
        }
        
        function tryPlayVideo(url) {
            const video = document.getElementById('testVideo');
            const statusDiv = document.getElementById('videoStatus');
            
            video.src = url;
            statusDiv.innerHTML = `<p>尝试播放: <code>${url}</code></p>`;
            
            video.addEventListener('loadstart', () => {
                statusDiv.innerHTML += '<p style="color: orange;">开始加载视频...</p>';
            });
            
            video.addEventListener('canplay', () => {
                statusDiv.innerHTML += '<p style="color: green;">视频可以播放！</p>';
            });
            
            video.addEventListener('error', (e) => {
                statusDiv.innerHTML += `<p style="color: red;">视频播放错误: ${e.message || '未知错误'}</p>`;
            });
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('videoStatus').innerHTML = '';
            document.getElementById('testVideo').src = '';
        }
    </script>
</body>
</html>
