# WebRTC 视频播放问题解决方案

## 问题描述

在飞控界面中尝试播放WebRTC协议的视频时遇到以下错误：
- `VIDEOJS: ERROR: The "flash" tech is undefined`
- `MEDIA_ERR_SRC_NOT_SUPPORTED: No compatible source was found for this media`

**原始WebRTC URL**: `webrtc://live2.sahy.cloud/live/7CTDM3900BM6WS-165-0-7`

## 问题原因

1. **Flash技术过时**: Video.js配置中包含了`flash`技术，但现代浏览器已不支持Flash
2. **WebRTC协议限制**: Video.js本身不直接支持WebRTC协议，需要特殊的信令服务器和WebRTC基础设施
3. **协议不兼容**: `webrtc://` 协议不被标准的HTML5视频播放器支持

## 已实施的解决方案

### 1. 移除Flash支持
```javascript
// 修改前
techOrder: ['html5', 'flash']

// 修改后  
techOrder: ['html5']
```

### 2. 添加WebRTC协议检测
- 自动检测`webrtc://`协议
- 显示友好的错误提示
- 提供替代方案对话框

### 3. 增强用户体验
- 添加"查看替代方案"按钮
- 提供多种可能的URL格式转换
- 用户友好的错误信息

## 推荐的替代方案

### 方案1: 使用HLS协议 (推荐)
```
https://live2.sahy.cloud/live/7CTDM3900BM6WS-165-0-7/index.m3u8
```

### 方案2: 使用RTMP协议
```
rtmp://live2.sahy.cloud/live/7CTDM3900BM6WS-165-0-7
```

### 方案3: 使用HTTP-FLV
```
https://live2.sahy.cloud/live/7CTDM3900BM6WS-165-0-7.flv
```

### 方案4: 使用MP4格式
```
https://live2.sahy.cloud/live/7CTDM3900BM6WS-165-0-7.mp4
```

## 如何使用

### 在VideoPlayer组件中
当检测到WebRTC URL时，组件会：
1. 显示错误信息："WebRTC协议需要特殊支持，当前播放器暂不支持此协议"
2. 提供"查看替代方案"按钮
3. 点击按钮后显示所有可能的替代URL格式
4. 用户可以尝试不同的URL格式

### 手动更换视频源
```javascript
// 在飞控界面中更换视频源
const newVideoSrc = 'https://live2.sahy.cloud/live/7CTDM3900BM6WS-165-0-7/index.m3u8'
// 更新视频源
this.videos[index].src = newVideoSrc
```

## 真正的WebRTC支持

如果需要真正的WebRTC支持，需要：

### 1. 服务器端配置
- WebRTC信令服务器
- STUN/TURN服务器
- 媒体服务器 (如Janus、Kurento等)

### 2. 客户端实现
- 使用WebRTC API
- 实现信令协议
- 处理ICE候选和媒体协商

### 3. 推荐的WebRTC库
- **Simple-peer**: 简化的WebRTC库
- **PeerJS**: 易用的WebRTC封装
- **Janus Gateway**: 专业的WebRTC网关

## 测试工具

项目中包含了一个测试页面 `test-webrtc-urls.html`，可以：
- 测试所有可能的URL格式
- 检查哪些URL可以访问
- 在浏览器中直接测试视频播放

## 联系服务提供商

建议联系视频服务提供商 (live2.sahy.cloud)：
1. 确认是否提供HLS格式的流
2. 获取正确的视频流URL
3. 了解支持的协议格式

## 总结

当前的修复方案：
- ✅ 解决了Flash技术错误
- ✅ 提供了友好的用户体验
- ✅ 给出了多种替代方案
- ✅ 保持了代码的稳定性

建议优先尝试HLS格式的URL，这是目前最广泛支持的流媒体协议。
